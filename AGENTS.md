# Repository Guidelines

## Project Structure & Module Organization
- Entry: `index.tsx` renders `App.tsx`.
- Source folders: `components/`, `views/`, `contexts/`, `services/`, `utils/`, `hooks/`.
- Types: `types.ts`.
- Vite config + alias: `vite.config.ts` defines `@/` → project root (use `@/components/...`).

## Build, Test, and Development Commands
- Install: `npm install`
- Dev server: `npm run dev` (Vite, hot reload)
- Production build: `npm run build` (outputs to `dist/`)
- Preview build: `npm run preview`
- Config: set `GEMINI_API_KEY` in `.env.local` (Vite exposes via `process.env.GEMINI_API_KEY`).

## Coding Style & Naming Conventions
- Language: TypeScript + React function components.
- Indentation: 2 spaces; single quotes; keep semicolons.
- Components: PascalCase files in `components/` (e.g., `TaskList.tsx`).
- Views: PascalCase in `views/` (e.g., `TasksView.tsx`).
- Contexts: `<Name>Context.tsx` in `contexts/`.
- Services: `<name>Service.ts` in `services/`.
- Hooks: `use<Name>.ts(x)` in `hooks/`.
- Imports: prefer alias `@/...` over relative chains.

## Testing Guidelines
- No test framework is configured yet. If adding tests, prefer Vitest + React Testing Library.
- File names: co-locate as `*.test.ts` / `*.test.tsx` or under `__tests__/`.
- Example scripts to add:
  - `"test": "vitest"`, `"test:watch": "vitest --watch"`.
- Aim for component tests for UI and unit tests for `utils/` and `services/`.

## Commit & Pull Request Guidelines
- Use Conventional Commits: `feat:`, `fix:`, `docs:`, `refactor:`, `chore:`, etc.
- Keep messages imperative and focused: `fix: handle null auth context`.
- PRs must include: concise description, before/after screenshots for UI, steps to verify, and linked issues.
- Scope changes to a feature/folder; include notes on any schema or env changes.

## Security & Configuration
- Do not commit secrets. Use `.env.local` for `GEMINI_API_KEY`.
- The in-browser DB persists to `localStorage` (`dbService.ts`); clear via app controls or `localStorage` when testing.
