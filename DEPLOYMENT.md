# Deploy to Vercel with Supabase

This app is now Supabase‑only (no local fallback). If anything is misconfigured, the app will fail fast so you can fix it.

## Overview

- Frontend: Vite + React, hosted on Vercel.
- Data/Auth: Supabase (Row Level Security enabled). No serverless functions required.
- Auth: Email/Password (simple mode without email confirmations).

## Prerequisites

- Supabase project
- Vercel account
- GitHub repo for this code
- Node.js for local runs

## 1) Supabase Setup (one time)

1. In Supabase Dashboard → Authentication → Providers:
   - Enable Email provider.
   - Turn OFF “Confirm email” for signups/logins (so you can sign up without SMTP).
2. Copy your keys from Project Settings → API:
   - Project URL (e.g. `https://xxxx.supabase.co`)
   - `anon` public key
3. Create tables and RLS policies:
   - Open SQL Editor.
   - Paste and run the contents of `supabase/schema.sql` (in this repo).

## 2) Local Development

1. Create `.env.local` in the project root (do not commit):

   ```bash
   VITE_SUPABASE_URL=your_supabase_project_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   # Optional
   GEMINI_API_KEY=
   ```

2. Install and run:

   ```bash
   npm install
   npm run dev
   ```

3. In the app:
   - Use “Create account” to sign up with email/password.
   - Log in and add tasks/bills/connections. Data is stored per user in Supabase.

Notes:
- There is no local DB fallback. If env vars are missing, you’ll see: “Supabase is not configured”.

## 3) Deploy to Vercel

1. Import your GitHub repo into Vercel.
2. Project → Settings → Environment Variables (set for Preview + Production):
   - `VITE_SUPABASE_URL`
   - `VITE_SUPABASE_ANON_KEY`
3. Build settings:
   - Framework: Vite (auto-detected)
   - Build command: `vite build`
   - Output directory: `dist`
4. Deploy. Then visit the site, sign up (email/password), and log in.

## Troubleshooting

- “Supabase is not configured. Define VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY.”
  - Set both env vars locally (in `.env.local`) and in Vercel Project Settings.
- 401/permission errors or empty data after login:
  - Ensure you are logged in (there’s a session).
  - Verify you ran `supabase/schema.sql` to create tables and RLS policies.
  - Auth → Providers: confirm Email is enabled. If confirmations are ON, you must configure SMTP.
- Signup fails:
  - If email confirmations are enabled, you’ll need to verify the email (or disable confirmations for simple testing).

## Security Notes

- The Supabase `anon` key is intended for client use and is safe to ship when Row Level Security (RLS) is enforced.
- Policies in `supabase/schema.sql` restrict access to only the authenticated user’s rows.
- Do not commit real secrets; use `.env.local` and Vercel project environment variables.

## What This Repo Includes

- `services/supabaseClient.ts` — creates the Supabase client and fails fast if env vars are missing.
- `services/supabaseService.ts` — all CRUD for tasks, settings, accounts, bills, connections (Supabase only).
- `contexts/*Context.tsx` — switched from local SQLite to Supabase calls; mounted only after auth.
- `components/auth/LoginPage.tsx` — email/password login and signup.
- `supabase/schema.sql` — tables + RLS policies to run in Supabase SQL Editor.
- `.env.example` — template for needed variables.

## Quick Commands

```bash
npm install
npm run dev
npm run build
```

That’s it — add the two env vars, run the schema once, and deploy.

