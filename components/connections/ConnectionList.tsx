
import React from 'react';
import { Connection } from '../../types';
import ConnectionCard from './ConnectionCard';
import { DocumentMagnifyingGlassIcon } from '../icons/Icons';
// FIX: Import getFollowUpStatus to calculate connection status for sorting.
import { getFollowUpStatus } from '../../utils/dateUtils';

interface ConnectionListProps {
    connections: Connection[];
}

const ConnectionList: React.FC<ConnectionListProps> = ({ connections }) => {
    
    // Sort connections: overdue first, then due soon, then ok.
    // FIX: Calculate follow-up status for each connection within the sort function as it does not exist on the Connection type.
    const sortedConnections = [...connections].sort((a, b) => {
        const statusOrder = { 'Overdue': 1, 'DueSoon': 2, 'Ok': 3 };
        const followUpA = getFollowUpStatus(a.lastContactDate, a.contactFrequency);
        const followUpB = getFollowUpStatus(b.lastContactDate, b.contactFrequency);

        const statusA = statusOrder[followUpA.status];
        const statusB = statusOrder[followUpB.status];
        if (statusA !== statusB) return statusA - statusB;
        // If status is the same, sort by days (ascending for due soon, descending for overdue)
        if (followUpA.status === 'Overdue') return followUpB.days - followUpA.days;
        return followUpA.days - followUpB.days;
    });

    return (
         <div className="bg-white rounded-xl shadow-sm border border-brand-border p-4">
            <h3 className="text-lg font-bold text-brand-primary mb-4 px-2">All Connections</h3>
            {connections.length === 0 ? (
                <div className="text-center py-20 px-6">
                    <DocumentMagnifyingGlassIcon className="mx-auto h-16 w-16 text-brand-border" />
                    <h3 className="mt-4 text-xl font-semibold text-brand-primary">No Connections Yet</h3>
                    <p className="mt-2 text-sm text-brand-secondary">
                        Add your first colleague to start tracking your professional network.
                    </p>
                </div>
            ) : (
                <div className="space-y-3">
                    {/* FIX: Use the sorted list of connections for rendering instead of the original unsorted list. */}
                    {sortedConnections.map(connection => (
                        <ConnectionCard key={connection.id} connection={connection} />
                    ))}
                </div>
            )}
        </div>
    );
};

export default ConnectionList;
