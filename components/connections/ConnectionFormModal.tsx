import React, { useState, useEffect, useContext } from 'react';
import { Connection, ContactFrequency } from '../../types';
import { ConnectionsContext } from '../../contexts/ConnectionsContext';
import { XMarkIcon } from '../icons/Icons';

interface ConnectionFormModalProps {
    isOpen: boolean;
    onClose: () => void;
    connectionToEdit?: Connection;
}

const ConnectionFormModal: React.FC<ConnectionFormModalProps> = ({ isOpen, onClose, connectionToEdit }) => {
    const [name, setName] = useState('');
    const [role, setRole] = useState('');
    const [company, setCompany] = useState('');
    const [contactFrequency, setContactFrequency] = useState<ContactFrequency>(ContactFrequency.Monthly);
    const [isSubmitting, setIsSubmitting] = useState(false);
    
    const connectionsContext = useContext(ConnectionsContext);

    useEffect(() => {
        if (connectionToEdit) {
            setName(connectionToEdit.name);
            setRole(connectionToEdit.role || '');
            setCompany(connectionToEdit.company || '');
            setContactFrequency(connectionToEdit.contactFrequency);
        } else {
            setName('');
            setRole('');
            setCompany('');
            setContactFrequency(ContactFrequency.Monthly);
        }
        setIsSubmitting(false);
    }, [connectionToEdit, isOpen]);

    if (!isOpen || !connectionsContext) return null;
    const { addConnection, updateConnection, deleteConnection } = connectionsContext;

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (isSubmitting) return;

        if (!name.trim()) {
            alert('Name is required.');
            return;
        }
        
        setIsSubmitting(true);

        const connectionData = {
            name,
            role,
            company,
            contactFrequency,
        };

        try {
            if (connectionToEdit) {
                await updateConnection({ ...connectionToEdit, ...connectionData });
            } else {
                await addConnection(connectionData);
            }
            onClose();
        } catch (error) {
            console.error("Failed to submit connection:", error);
            setIsSubmitting(false);
        }
    };

    const handleDelete = async () => {
        if (isSubmitting) return;
        if (connectionToEdit && window.confirm(`Are you sure you want to permanently delete ${connectionToEdit.name}?`)) {
            setIsSubmitting(true);
            try {
                await deleteConnection(connectionToEdit.id);
                onClose();
            } catch (error) {
                console.error("Failed to delete connection:", error);
                setIsSubmitting(false);
            }
        }
    };

    const inputBaseClasses = "mt-1 block w-full rounded-md border-brand-border bg-brand-bg-alt text-brand-primary shadow-sm focus:border-brand-secondary focus:ring-brand-secondary sm:text-sm transition";

    return (
        <div className="fixed inset-0 bg-brand-primary/20 backdrop-blur-sm flex justify-center items-center z-50 p-4 animate-fade-in" role="dialog" aria-modal="true">
            <div className="bg-white border border-brand-border rounded-lg shadow-xl w-full max-w-lg max-h-[90vh] flex flex-col transform animate-zoom-in">
                <header className="flex items-center justify-between p-5 border-b border-brand-border">
                    <h2 className="text-xl font-bold text-brand-primary">{connectionToEdit ? 'Edit Connection' : 'Add New Connection'}</h2>
                    <button onClick={onClose} className="p-1 rounded-full text-brand-secondary hover:bg-brand-bg-alt focus:outline-none focus:ring-2 focus:ring-brand-secondary">
                        <XMarkIcon className="w-6 h-6" />
                    </button>
                </header>
                <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto custom-scrollbar p-6 space-y-6">
                    <div>
                        <label htmlFor="name" className="block text-sm font-medium text-brand-secondary">Full Name</label>
                        <input type="text" id="name" value={name} onChange={e => setName(e.target.value)} required className={inputBaseClasses} />
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                         <div>
                            <label htmlFor="role" className="block text-sm font-medium text-brand-secondary">Role</label>
                            <input type="text" id="role" value={role} onChange={e => setRole(e.target.value)} className={inputBaseClasses} />
                        </div>
                        <div>
                            <label htmlFor="company" className="block text-sm font-medium text-brand-secondary">Company</label>
                            <input type="text" id="company" value={company} onChange={e => setCompany(e.target.value)} className={inputBaseClasses} />
                        </div>
                    </div>
                    <div>
                        <label htmlFor="contactFrequency" className="block text-sm font-medium text-brand-secondary">Contact Frequency</label>
                        <select id="contactFrequency" value={contactFrequency} onChange={e => setContactFrequency(e.target.value as ContactFrequency)} className={inputBaseClasses}>
                            {Object.values(ContactFrequency).map(f => <option key={f} value={f}>{f}</option>)}
                        </select>
                         <p className="mt-2 text-xs text-brand-secondary">
                            How often you'd like to stay in touch. The "Last Contact Date" is updated when you click "Mark as Contacted".
                        </p>
                    </div>
                </form>
                <footer className="flex justify-between items-center p-4 bg-gray-50 border-t border-brand-border">
                    <div>
                        {connectionToEdit && (
                            <button
                                type="button"
                                onClick={handleDelete}
                                disabled={isSubmitting}
                                className="px-4 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-500/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 focus:ring-offset-gray-50 transition-colors disabled:opacity-50"
                            >
                                Delete
                            </button>
                        )}
                    </div>
                    <div className="space-x-3">
                        <button type="button" onClick={onClose} disabled={isSubmitting} className="px-4 py-2 border border-brand-border text-sm font-medium rounded-md shadow-sm text-brand-primary bg-white hover:bg-brand-bg-alt focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-gray-50 disabled:opacity-50">Cancel</button>
                        <button type="submit" onClick={handleSubmit} disabled={isSubmitting} className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-brand-primary hover:bg-brand-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-gray-50 disabled:opacity-50">{connectionToEdit ? 'Save Changes' : 'Add Connection'}</button>
                    </div>
                </footer>
            </div>
        </div>
    );
};

export default ConnectionFormModal;