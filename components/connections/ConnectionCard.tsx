
import React, { useState, useContext } from 'react';
import { Connection } from '../../types';
import { ConnectionsContext } from '../../contexts/ConnectionsContext';
import ConnectionFormModal from './ConnectionFormModal';
import { getFollowUpStatus, formatLastContact, FollowUpStatus } from '../../utils/dateUtils';
import { PencilIcon, TrashIcon, ArrowPathIcon } from '../icons/Icons';

interface ConnectionCardProps {
    connection: Connection;
}

const statusClasses = {
    [FollowUpStatus.Overdue]: {
        bar: 'bg-rose-500',
        text: 'text-rose-600 font-medium',
        message: (days: number) => `Overdue by ${days} day${days > 1 ? 's' : ''}`,
    },
    [FollowUpStatus.DueSoon]: {
        bar: 'bg-yellow-400',
        text: 'text-yellow-600 font-medium',
        message: (days: number) => days > 0 ? `Due in ${days} day${days > 1 ? 's' : ''}`: 'Due today',
    },
    [FollowUpStatus.Ok]: {
        bar: 'bg-green-500',
        text: 'text-green-600',
        message: (days: number) => `Due in ${days} days`,
    },
};

const ConnectionCard: React.FC<ConnectionCardProps> = ({ connection }) => {
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const connectionsContext = useContext(ConnectionsContext);

    if (!connectionsContext) throw new Error("ConnectionCard must be used within a ConnectionsProvider");
    const { updateConnection, deleteConnection } = connectionsContext;
    
    const handleMarkAsContacted = () => {
        updateConnection({ ...connection, lastContactDate: new Date().toISOString() });
    };

    const handleDelete = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (window.confirm(`Are you sure you want to permanently delete ${connection.name}?`)) {
            deleteConnection(connection.id);
        }
    };

    const handleEdit = (e: React.MouseEvent) => {
        e.stopPropagation();
        setIsEditModalOpen(true);
    };

    const followUp = getFollowUpStatus(connection.lastContactDate, connection.contactFrequency);
    const statusInfo = statusClasses[followUp.status];
    
    return (
        <>
            <div className="relative group flex items-center space-x-4 bg-gray-50/70 hover:bg-white rounded-lg p-3 border border-brand-border transition-all">
                <div className={`absolute left-0 top-0 bottom-0 w-1.5 rounded-l-lg ${statusInfo.bar}`}></div>
                <div className="pl-2 flex-1 min-w-0" onClick={handleEdit}>
                    <p className="text-lg font-medium text-brand-primary truncate">{connection.name}</p>
                    <p className="text-sm text-brand-secondary truncate">{connection.role}{connection.company && ` at ${connection.company}`}</p>
                    
                    <div className="mt-2 text-sm text-brand-secondary">
                        <span>Last contacted: {formatLastContact(connection.lastContactDate)}</span>
                        <span className="mx-2">&bull;</span>
                        <span className={statusInfo.text}>{statusInfo.message(followUp.days)}</span>
                    </div>
                </div>

                <div className="flex items-center space-x-2">
                    <button 
                        onClick={handleMarkAsContacted}
                        className="px-3 py-1.5 text-sm font-medium rounded-md text-brand-primary bg-white border border-brand-border hover:bg-brand-bg-alt focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary transition-all transform hover:scale-105"
                    >
                        Mark as Contacted
                    </button>
                    <div className="flex items-center opacity-0 group-hover:opacity-100 transition-opacity">
                         <button 
                            onClick={handleEdit}
                            className="p-1.5 text-brand-secondary hover:text-brand-primary hover:bg-brand-bg-alt rounded-md focus:outline-none focus:ring-2 focus:ring-brand-secondary"
                            aria-label="Edit connection"
                        >
                            <PencilIcon className="w-5 h-5" />
                        </button>
                        <button 
                            onClick={handleDelete}
                            className="p-1.5 text-brand-secondary hover:text-brand-primary hover:bg-brand-bg-alt rounded-md focus:outline-none focus:ring-2 focus:ring-brand-secondary"
                            aria-label="Delete connection"
                        >
                            <TrashIcon className="w-5 h-5" />
                        </button>
                    </div>
                </div>
            </div>

            {isEditModalOpen && (
                <ConnectionFormModal
                    isOpen={isEditModalOpen}
                    onClose={() => setIsEditModalOpen(false)}
                    connectionToEdit={connection}
                />
            )}
        </>
    );
};

export default ConnectionCard;