
import React, { useMemo } from 'react';
import { Connection } from '../../types';
import { getFollowUpStatus, FollowUpStatus } from '../../utils/dateUtils';

interface SummaryCardsProps {
    connections: Connection[];
}

const SummaryCard: React.FC<{ title: string; value: number | string; icon: React.ReactNode }> = ({ title, value, icon }) => (
    <div className="bg-white p-5 rounded-xl border border-brand-border shadow-sm flex items-center space-x-4">
        <div className="flex-shrink-0 w-12 h-12 flex items-center justify-center bg-brand-bg-alt rounded-full">
            {icon}
        </div>
        <div>
            <p className="text-sm font-medium text-brand-secondary">{title}</p>
            <p className="text-2xl font-bold text-brand-primary">{value}</p>
        </div>
    </div>
);

const SummaryCards: React.FC<SummaryCardsProps> = ({ connections }) => {
    const summary = useMemo(() => {
        const total = connections.length;
        
        const overdue = connections.filter(c => 
            getFollowUpStatus(c.lastContactDate, c.contactFrequency).status === FollowUpStatus.Overdue
        ).length;

        const contactedLast7Days = connections.filter(c => {
            const lastContact = new Date(c.lastContactDate);
            const today = new Date();
            const diffDays = (today.getTime() - lastContact.getTime()) / (1000 * 3600 * 24);
            return diffDays <= 7;
        }).length;

        return { total, overdue, contactedLast7Days };
    }, [connections]);

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <SummaryCard 
                title="Total Colleagues" 
                value={summary.total} 
                icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-brand-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg>}
            />
            <SummaryCard 
                title="Overdue Contacts" 
                value={summary.overdue}
                icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-rose-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>}
            />
             <SummaryCard 
                title="Contacted This Week" 
                value={summary.contactedLast7Days}
                icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>}
            />
        </div>
    );
};

export default SummaryCards;