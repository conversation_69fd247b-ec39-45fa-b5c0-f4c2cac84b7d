
import React from 'react';
import { View } from '../../types';
import { PlusIcon } from '../icons/Icons';
import TaskFormModal from '../tasks/TaskFormModal';
import AccountFormModal from '../bills/AccountFormModal';
import ConnectionFormModal from '../connections/ConnectionFormModal';

interface HeaderProps {
    currentView: View;
}

const viewTitles: Record<View, string> = {
    tasks: 'Tasks',
    bills: 'Bills Dashboard',
    connections: 'My Connections',
    settings: 'Settings',
    profile: 'User Profile'
};

const Header: React.FC<HeaderProps> = ({ currentView }) => {
    const [isTaskModalOpen, setIsTaskModalOpen] = React.useState(false);
    const [isAccountModalOpen, setIsAccountModalOpen] = React.useState(false);
    const [isConnectionModalOpen, setIsConnectionModalOpen] = React.useState(false);

    const renderButton = () => {
        switch(currentView) {
            case 'tasks':
                return (
                    <button
                        onClick={() => setIsTaskModalOpen(true)}
                        className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-brand-primary hover:bg-brand-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-white transition-transform transform hover:scale-105"
                    >
                        <PlusIcon className="w-5 h-5 mr-2 -ml-1" />
                        New Task
                    </button>
                );
            case 'bills':
                 return (
                    <button
                        onClick={() => setIsAccountModalOpen(true)}
                        className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-brand-primary hover:bg-brand-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-white transition-transform transform hover:scale-105"
                    >
                        <PlusIcon className="w-5 h-5 mr-2 -ml-1" />
                        Add Account
                    </button>
                );
            case 'connections':
                 return (
                    <button
                        onClick={() => setIsConnectionModalOpen(true)}
                        className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-brand-primary hover:bg-brand-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-white transition-transform transform hover:scale-105"
                    >
                        <PlusIcon className="w-5 h-5 mr-2 -ml-1" />
                        Add Connection
                    </button>
                );
            default:
                return null;
        }
    }
    
    return (
        <>
            <header className="flex-shrink-0 bg-white/80 backdrop-blur-sm border-b border-brand-bg-alt h-20 px-4 sm:px-6 lg:px-8">
                <div className="flex items-center justify-between h-full">
                    <h1 className="text-2xl font-bold text-brand-primary">{viewTitles[currentView]}</h1>
                    {renderButton()}
                </div>
            </header>
            <TaskFormModal
                isOpen={isTaskModalOpen}
                onClose={() => setIsTaskModalOpen(false)}
            />
            <AccountFormModal
                isOpen={isAccountModalOpen}
                onClose={() => setIsAccountModalOpen(false)}
            />
            <ConnectionFormModal
                isOpen={isConnectionModalOpen}
                onClose={() => setIsConnectionModalOpen(false)}
            />
        </>
    );
};

export default Header;