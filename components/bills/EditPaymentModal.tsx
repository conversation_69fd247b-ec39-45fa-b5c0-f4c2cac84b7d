import React, { useState, useEffect, useContext } from 'react';
import { Bill, PaymentMethod } from '../../types';
import { BillsContext } from '../../contexts/BillsContext';
import { XMarkIcon } from '../icons/Icons';

interface EditPaymentModalProps {
    isOpen: boolean;
    onClose: () => void;
    bill: Bill;
}

const EditPaymentModal: React.FC<EditPaymentModalProps> = ({ isOpen, onClose, bill }) => {
    // Form state
    const [dueDate, setDueDate] = useState('');
    const [statementBalance, setStatementBalance] = useState('');
    const [minPayment, setMinPayment] = useState('');
    const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('Manual');
    const [notes, setNotes] = useState('');
    const [extraPrincipal, setExtraPrincipal] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Payment-specific state
    const [isPaid, setIsPaid] = useState(false);
    const [paidAmount, setPaidAmount] = useState('');
    const [paidDate, setPaidDate] = useState('');

    const billsContext = useContext(BillsContext);

    // Find the associated account
    const account = billsContext?.accounts.find(a => a.id === bill.accountId);

    useEffect(() => {
        if (bill) {
            setDueDate(bill.dueDate);
            setStatementBalance(String(bill.statementBalance));
            setMinPayment(String(bill.minPayment));
            setPaymentMethod(bill.paymentMethod);
            setNotes(bill.notes || '');
            setExtraPrincipal(String(bill.extraPrincipal || ''));
            
            setIsPaid(bill.isPaid);
            setPaidAmount(String(bill.paidAmount || ''));
            setPaidDate(bill.paidDate ? bill.paidDate.split('T')[0] : new Date().toISOString().split('T')[0]);
        }
        setIsSubmitting(false);
    }, [bill, isOpen]);

    if (!isOpen || !billsContext || !account) return null;

    const { updateBill, deleteBill } = billsContext;

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (isSubmitting) return;

        if (!dueDate || !statementBalance || !minPayment) {
            alert('Due Date, Statement Balance, and Minimum Payment are required.');
            return;
        }

        setIsSubmitting(true);

        const updatedBillData = {
            ...bill,
            dueDate,
            statementBalance: parseFloat(statementBalance),
            minPayment: parseFloat(minPayment),
            paymentMethod,
            notes: notes || undefined,
            extraPrincipal: extraPrincipal ? parseFloat(extraPrincipal) : undefined,
            isPaid,
            paidAmount: isPaid && paidAmount ? parseFloat(paidAmount) : undefined,
            paidDate: isPaid && paidDate ? new Date(paidDate).toISOString() : undefined,
        };
        
        // When marking as unpaid, clear payment details
        if (!isPaid) {
            updatedBillData.paidAmount = undefined;
            updatedBillData.paidDate = undefined;
        }

        try {
            await updateBill(updatedBillData);
            onClose();
        } catch (error) {
            console.error("Failed to update bill:", error);
            setIsSubmitting(false);
        }
    };

    const handleDelete = async () => {
        if (isSubmitting) return;

        if (window.confirm("Are you sure you want to permanently delete this bill? This action cannot be undone.")) {
            setIsSubmitting(true);
            try {
                await deleteBill(bill.id);
                onClose();
            } catch (error) {
                console.error("Failed to delete bill:", error);
                setIsSubmitting(false);
            }
        }
    };

    const inputBaseClasses = "mt-1 block w-full rounded-md border-brand-border bg-brand-bg-alt text-brand-primary shadow-sm focus:border-brand-secondary focus:ring-brand-secondary sm:text-sm transition";

    return (
        <div className="fixed inset-0 bg-brand-primary/20 backdrop-blur-sm flex justify-center items-center z-50 p-4 animate-fade-in" role="dialog" aria-modal="true" aria-labelledby="edit-bill-title">
            <div className="bg-white border border-brand-border rounded-lg shadow-xl w-full max-w-lg max-h-[90vh] flex flex-col transform animate-zoom-in">
                <header className="flex items-center justify-between p-5 border-b border-brand-border">
                    <div>
                        <h2 id="edit-bill-title" className="text-xl font-bold text-brand-primary">Edit Bill</h2>
                        <p className="text-sm text-brand-secondary">{account.name}</p>
                    </div>
                    <button onClick={onClose} className="p-1 rounded-full text-brand-secondary hover:bg-brand-bg-alt focus:outline-none focus:ring-2 focus:ring-brand-secondary" aria-label="Close modal">
                        <XMarkIcon className="w-6 h-6" />
                    </button>
                </header>
                <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto custom-scrollbar p-6 space-y-6">
                    {/* Bill Details */}
                    <fieldset>
                        <legend className="text-sm font-medium text-brand-secondary">Bill Details</legend>
                        <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div>
                                <label htmlFor="dueDate" className="block text-sm font-medium text-brand-secondary">Due Date</label>
                                <input type="date" id="dueDate" value={dueDate} onChange={e => setDueDate(e.target.value)} required className={inputBaseClasses} />
                            </div>
                             <div>
                                <label className="block text-sm font-medium text-brand-secondary">Payment Method</label>
                                <div className="mt-2 flex gap-4">
                                    <label className="flex items-center">
                                        <input type="radio" name="paymentMethod" value="Manual" checked={paymentMethod === 'Manual'} onChange={() => setPaymentMethod('Manual')} className="h-4 w-4 text-brand-secondary focus:ring-brand-secondary border-brand-border" />
                                        <span className="ml-2 text-sm text-brand-primary">Manual</span>
                                    </label>
                                     <label className="flex items-center">
                                        <input type="radio" name="paymentMethod" value="Auto Pay" checked={paymentMethod === 'Auto Pay'} onChange={() => setPaymentMethod('Auto Pay')} className="h-4 w-4 text-brand-secondary focus:ring-brand-secondary border-brand-border" />
                                        <span className="ml-2 text-sm text-brand-primary">Auto Pay</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                         <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 gap-6">
                             <div>
                                <label htmlFor="statementBalance" className="block text-sm font-medium text-brand-secondary">Statement Balance</label>
                                <div className="relative mt-1">
                                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"><span className="text-brand-secondary sm:text-sm">$</span></div>
                                    <input type="number" id="statementBalance" value={statementBalance} onChange={e => setStatementBalance(e.target.value)} required className={`${inputBaseClasses} pl-7`} placeholder="0.00" step="0.01" />
                                </div>
                            </div>
                            <div>
                                <label htmlFor="minPayment" className="block text-sm font-medium text-brand-secondary">Minimum Payment</label>
                                <div className="relative mt-1">
                                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"><span className="text-brand-secondary sm:text-sm">$</span></div>
                                    <input type="number" id="minPayment" value={minPayment} onChange={e => setMinPayment(e.target.value)} required className={`${inputBaseClasses} pl-7`} placeholder="0.00" step="0.01" />
                                </div>
                            </div>
                        </div>
                        <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div>
                                <label htmlFor="notes" className="block text-sm font-medium text-brand-secondary">Notes (Optional)</label>
                                <textarea id="notes" value={notes} onChange={e => setNotes(e.target.value)} rows={2} className={inputBaseClasses}></textarea>
                            </div>
                             <div>
                                <label htmlFor="extraPrincipal" className="block text-sm font-medium text-brand-secondary">Extra Principal (Optional)</label>
                                 <div className="relative mt-1">
                                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"><span className="text-brand-secondary sm:text-sm">$</span></div>
                                    <input type="number" id="extraPrincipal" value={extraPrincipal} onChange={e => setExtraPrincipal(e.target.value)} className={`${inputBaseClasses} pl-7`} placeholder="0.00" step="0.01" />
                                </div>
                            </div>
                        </div>
                    </fieldset>

                    {/* Payment Details */}
                    <fieldset className="p-4 bg-gray-50 rounded-lg">
                        <legend className="text-sm font-medium text-brand-secondary">Payment Status</legend>
                        <div className="mt-4">
                            <div className="relative flex items-start">
                                <div className="flex h-6 items-center">
                                    <input id="isPaid" name="isPaid" type="checkbox" checked={isPaid} onChange={e => setIsPaid(e.target.checked)} className="h-4 w-4 rounded border-brand-border text-brand-secondary focus:ring-brand-secondary" />
                                </div>
                                <div className="ml-3 text-sm leading-6">
                                    <label htmlFor="isPaid" className="font-medium text-brand-primary">Mark as Paid</label>
                                    <p className="text-brand-secondary">Check this box if this bill has been paid.</p>
                                </div>
                            </div>
                        </div>
                        {isPaid && (
                            <div className="mt-4 space-y-6 animate-fade-in">
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                    <div>
                                        <label htmlFor="paidDate" className="block text-sm font-medium text-brand-secondary">Payment Date</label>
                                        <input type="date" id="paidDate" value={paidDate} onChange={e => setPaidDate(e.target.value)} className={inputBaseClasses} />
                                    </div>
                                    <div>
                                        <label htmlFor="paidAmount" className="block text-sm font-medium text-brand-secondary">Amount Paid</label>
                                        <div className="relative mt-1">
                                            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"><span className="text-brand-secondary sm:text-sm">$</span></div>
                                            <input type="number" id="paidAmount" value={paidAmount} onChange={e => setPaidAmount(e.target.value)} className={`${inputBaseClasses} pl-7`} placeholder={statementBalance || "0.00"} step="0.01" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </fieldset>
                </form>
                 <footer className="flex justify-between items-center p-4 bg-gray-50 border-t border-brand-border">
                    <div>
                        <button
                            type="button"
                            onClick={handleDelete}
                            disabled={isSubmitting}
                            className="px-4 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-500/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 focus:ring-offset-gray-50 transition-colors disabled:opacity-50"
                        >
                            Delete Bill
                        </button>
                    </div>
                    <div className="space-x-3">
                        <button type="button" onClick={onClose} disabled={isSubmitting} className="px-4 py-2 border border-brand-border text-sm font-medium rounded-md shadow-sm text-brand-primary bg-white hover:bg-brand-bg-alt focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-gray-50 disabled:opacity-50">Cancel</button>
                        <button type="submit" onClick={handleSubmit} disabled={isSubmitting} className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-brand-primary hover:bg-brand-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-gray-50 disabled:opacity-50">Save Changes</button>
                    </div>
                </footer>
            </div>
        </div>
    );
};

export default EditPaymentModal;