import React, { useState, useEffect, useContext } from 'react';
import { Account, Bill } from '../../types';
import { BillsContext } from '../../contexts/BillsContext';
import { XMarkIcon } from '../icons/Icons';

interface PayBillModalProps {
    isOpen: boolean;
    onClose: () => void;
    bill: Bill;
    account: Account;
}

const formatCurrency = (value: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);

const PayBillModal: React.FC<PayBillModalProps> = ({ isOpen, onClose, bill, account }) => {
    const [paymentAmount, setPaymentAmount] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const billsContext = useContext(BillsContext);

    useEffect(() => {
        if (isOpen) {
            // Pre-fill with statement balance
            setPaymentAmount(String(bill.statementBalance));
            setIsSubmitting(false);
        }
    }, [isOpen, bill.statementBalance]);

    if (!isOpen || !billsContext) return null;

    const { updateBill } = billsContext;

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (isSubmitting) return;

        const amount = parseFloat(paymentAmount);
        if (isNaN(amount) || amount <= 0) {
            alert('Please enter a valid payment amount.');
            return;
        }

        setIsSubmitting(true);

        const updatedBill: Bill = {
            ...bill,
            isPaid: true,
            paidAmount: amount,
            paidDate: new Date().toISOString(),
        };

        const newBalance = account.balance - amount;
        
        try {
            await updateBill(updatedBill, newBalance);
            onClose();
        } catch (error) {
            console.error("Failed to pay bill:", error);
            setIsSubmitting(false);
        }
    };

    const inputBaseClasses = "mt-1 block w-full rounded-md border-brand-border bg-brand-bg-alt text-brand-primary shadow-sm focus:border-brand-secondary focus:ring-brand-secondary sm:text-sm transition";

    return (
        <div className="fixed inset-0 bg-brand-primary/20 backdrop-blur-sm flex justify-center items-center z-50 p-4 animate-fade-in" role="dialog" aria-modal="true">
            <div className="bg-white border border-brand-border rounded-lg shadow-xl w-full max-w-md max-h-[90vh] flex flex-col transform animate-zoom-in">
                <header className="flex items-center justify-between p-5 border-b border-brand-border">
                    <h2 className="text-xl font-bold text-brand-primary">Pay Bill: {account.name}</h2>
                    <button onClick={onClose} className="p-1 rounded-full text-brand-secondary hover:bg-brand-bg-alt focus:outline-none focus:ring-2 focus:ring-brand-secondary">
                        <XMarkIcon className="w-6 h-6" />
                    </button>
                </header>
                <form onSubmit={handleSubmit}>
                    <div className="p-6 space-y-4">
                        <div className="p-4 bg-gray-50 rounded-lg text-sm">
                            <div className="flex justify-between">
                                <span className="text-brand-secondary">Statement Balance:</span>
                                <span className="font-medium text-brand-primary">{formatCurrency(bill.statementBalance)}</span>
                            </div>
                            <div className="flex justify-between mt-1">
                                <span className="text-brand-secondary">Minimum Payment:</span>
                                <span className="font-medium text-brand-primary">{formatCurrency(bill.minPayment)}</span>
                            </div>
                            <div className="flex justify-between mt-1">
                                <span className="text-brand-secondary">Due Date:</span>
                                <span className="font-medium text-brand-primary">{new Date(bill.dueDate).toLocaleDateString()}</span>
                            </div>
                        </div>
                        <div>
                            <label htmlFor="paymentAmount" className="block text-sm font-medium text-brand-secondary">Payment Amount</label>
                            <div className="relative mt-1">
                                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"><span className="text-brand-secondary sm:text-sm">$</span></div>
                                <input
                                    type="number"
                                    id="paymentAmount"
                                    value={paymentAmount}
                                    onChange={e => setPaymentAmount(e.target.value)}
                                    required
                                    className={`${inputBaseClasses} pl-7`}
                                    placeholder="0.00"
                                    step="0.01"
                                    autoFocus
                                />
                            </div>
                        </div>
                    </div>
                    <footer className="flex justify-end items-center p-4 bg-gray-50 border-t border-brand-border space-x-3">
                        <button type="button" onClick={onClose} disabled={isSubmitting} className="px-4 py-2 border border-brand-border text-sm font-medium rounded-md shadow-sm text-brand-primary bg-white hover:bg-brand-bg-alt focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-gray-50 disabled:opacity-50">Cancel</button>
                        <button type="submit" disabled={isSubmitting} className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-brand-primary hover:bg-brand-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-gray-50 disabled:opacity-50">Confirm Payment</button>
                    </footer>
                </form>
            </div>
        </div>
    );
};

export default PayBillModal;