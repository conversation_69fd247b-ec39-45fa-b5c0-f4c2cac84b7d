
import React, { useState, useMemo } from 'react';
import { Account, Bill, AccountType } from '../../types';
import AccountFormModal from './AccountFormModal';
import { formatBillDueDate } from '../../utils/dateUtils';
import { PencilIcon } from '../icons/Icons';

interface AccountCardProps {
    account: Account;
    bills: Bill[];
    onEditBill: (bill: Bill) => void;
}

const formatCurrency = (value: number | undefined) => {
    if (value === undefined) return '';
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
};

const ProgressBar: React.FC<{ value: number; max: number; colorClass: string }> = ({ value, max, colorClass }) => {
    const percentage = max > 0 ? (value / max) * 100 : 0;
    return (
        <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div className={`${colorClass} h-1.5 rounded-full`} style={{ width: `${percentage}%` }}></div>
        </div>
    );
};

const AccountCard: React.FC<AccountCardProps> = ({ account, bills, onEditBill }) => {
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);

    const nextBill = useMemo(() => {
        return bills
            .filter(b => b.accountId === account.id && !b.isPaid)
            .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())[0];
    }, [bills, account.id]);

    const lastPaidBill = useMemo(() => {
        return bills
            .filter(b => b.accountId === account.id && b.isPaid && b.paidDate)
            .sort((a, b) => new Date(b.paidDate!).getTime() - new Date(a.paidDate!).getTime())[0];
    }, [bills, account.id]);
    
    const { text: dueDateText, isOverdue } = nextBill ? formatBillDueDate(nextBill.dueDate) : { text: '', isOverdue: false };
    
    const utilization = account.type === AccountType.CreditCard && account.creditLimit && account.creditLimit > 0
        ? (account.balance / account.creditLimit) * 100
        : 0;
    
    let utilizationColor = 'bg-green-500';
    if (utilization > 70) utilizationColor = 'bg-red-500';
    else if (utilization > 30) utilizationColor = 'bg-yellow-400';

    return (
        <>
            <div className="bg-gray-50/70 hover:bg-white border border-brand-border rounded-lg p-3 transition-all">
                <div 
                    className="flex items-center justify-between cursor-pointer"
                    onClick={() => setIsEditModalOpen(true)}
                >
                    <div>
                        <p className="font-semibold text-brand-primary">
                            {account.name} {account.lastFourDigits && `(...${account.lastFourDigits})`}
                        </p>
                        <p className="text-sm text-brand-secondary">{account.institution || account.type}</p>
                    </div>
                    <div className="text-right">
                        <p className="font-semibold text-brand-primary">{formatCurrency(account.balance)}</p>
                        {lastPaidBill && lastPaidBill.paidDate && typeof lastPaidBill.paidAmount === 'number' ? (
                            <p className="text-xs text-brand-secondary">
                                Last paid: {new Date(lastPaidBill.paidDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} for {formatCurrency(lastPaidBill.paidAmount)}
                            </p>
                        ) : (
                             <p className="text-xs text-brand-secondary">No payment history</p>
                        )}
                    </div>
                </div>
                {account.type === AccountType.CreditCard && account.creditLimit && (
                     <div className="mt-2 cursor-pointer" onClick={() => setIsEditModalOpen(true)}>
                        <ProgressBar value={account.balance} max={account.creditLimit} colorClass={utilizationColor} />
                    </div>
                )}
                
                {(nextBill || lastPaidBill) && account.type !== AccountType.Cash && <hr className="my-2 border-t border-brand-border/60" />}

                {nextBill ? (
                    <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-3">
                             <span className={`px-2 py-0.5 rounded-full text-xs font-medium whitespace-nowrap ${nextBill.paymentMethod === 'Auto Pay' ? 'bg-blue-100 text-blue-800' : 'bg-gray-200 text-gray-800'}`}>
                                {nextBill.paymentMethod}
                            </span>
                            <div>
                                <p className={`font-semibold ${isOverdue ? 'text-rose-600' : 'text-brand-primary'}`}>{dueDateText}</p>
                                <p className="text-brand-secondary">{formatCurrency(nextBill.minPayment)} min payment</p>
                            </div>
                        </div>

                        <button 
                            onClick={(e) => { e.stopPropagation(); onEditBill(nextBill); }}
                            className="p-1.5 text-brand-secondary hover:text-brand-primary hover:bg-brand-bg-alt rounded-md focus:outline-none focus:ring-2 focus:ring-brand-secondary"
                            aria-label={`Edit next bill for ${account.name}`}
                        >
                            <PencilIcon className="w-4 h-4" />
                        </button>
                    </div>
                ) : lastPaidBill ? (
                     <div className="flex items-center justify-between text-sm">
                        <p className="text-brand-secondary">No upcoming payments. You can edit the last recorded payment.</p>
                        <button 
                            onClick={(e) => { e.stopPropagation(); onEditBill(lastPaidBill); }}
                            className="p-1.5 text-brand-secondary hover:text-brand-primary hover:bg-brand-bg-alt rounded-md focus:outline-none focus:ring-2 focus:ring-brand-secondary ml-4 flex-shrink-0"
                            aria-label={`Edit last paid bill for ${account.name}`}
                        >
                            <PencilIcon className="w-4 h-4" />
                        </button>
                    </div>
                ) : (
                    account.type !== AccountType.Cash && (
                        <div className="pt-1 text-center">
                             <p className="text-xs text-brand-secondary">No upcoming payments</p>
                        </div>
                    )
                )}
            </div>
            
            {isEditModalOpen && (
                <AccountFormModal
                    isOpen={isEditModalOpen}
                    onClose={() => setIsEditModalOpen(false)}
                    accountToEdit={account}
                />
            )}
        </>
    );
};

export default AccountCard;
