import React, { useState, useMemo, useContext } from 'react';
import { Bill, Account } from '../../types';
import { BillsContext } from '../../contexts/BillsContext';
import BillRow from './BillRow';
import BillFormModal from './BillFormModal';
import PayBillModal from './PayBillModal';
import { PlusIcon } from '../icons/Icons';
import EditPaymentModal from './EditPaymentModal';

interface UpcomingBillsProps {
    bills: Bill[];
    accounts: Account[];
}

const UpcomingBills: React.FC<UpcomingBillsProps> = ({ bills, accounts }) => {
    const [isBillFormModalOpen, setIsBillFormModalOpen] = useState(false);
    const [billForForm, setBillForForm] = useState<Bill | undefined>(undefined);
    const [billToPay, setBillToPay] = useState<{bill: Bill, account: Account} | null>(null);
    const [showPaid, setShowPaid] = useState(false);

    const [isEditPaymentModalOpen, setIsEditPaymentModalOpen] = useState(false);
    const [billToEditPayment, setBillToEditPayment] = useState<Bill | null>(null);

    const billsContext = useContext(BillsContext);
    if (!billsContext) throw new Error("UpcomingBills must be used within a BillsProvider");

    const displayedBills = useMemo(() => {
        const filtered = bills.filter(b => showPaid || !b.isPaid);

        return filtered.sort((a, b) => {
            if (a.isPaid && !b.isPaid) return 1;
            if (!a.isPaid && b.isPaid) return -1;
            
            if (a.isPaid && b.isPaid) {
                // Both are paid, sort by paidDate descending
                return new Date(b.paidDate!).getTime() - new Date(a.paidDate!).getTime();
            }
            
            // Both are unpaid, sort by dueDate ascending
            return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
        });
    }, [bills, showPaid]);

    const lastPaidBillsByAccount = useMemo(() => {
        const map = new Map<string, Bill>();
        const paidBills = bills.filter(b => b.isPaid && b.paidDate);
        
        for(const bill of paidBills) {
            const existing = map.get(bill.accountId);
            if (!existing || new Date(bill.paidDate!).getTime() > new Date(existing.paidDate!).getTime()) {
                map.set(bill.accountId, bill);
            }
        }
        return map;
    }, [bills]);
    
    const handleEditPayment = (bill: Bill) => {
        setBillToEditPayment(bill);
        setIsEditPaymentModalOpen(true);
    };
    
    const handlePayBill = (bill: Bill, account: Account) => {
        setBillToPay({ bill, account });
    };
    
    const handleAddBill = () => {
        setBillForForm(undefined);
        setIsBillFormModalOpen(true);
    }
    
    const handleCloseModals = () => {
        setIsBillFormModalOpen(false);
        setBillForForm(undefined);
        setBillToPay(null);
        setIsEditPaymentModalOpen(false);
        setBillToEditPayment(null);
    }

    return (
        <>
            <div className="bg-white rounded-xl shadow-sm border border-brand-border">
                <header className="flex items-center justify-between p-4 border-b border-brand-border flex-wrap gap-4">
                    <h3 className="text-lg font-bold text-brand-primary">Upcoming Bills</h3>
                    <div className="flex items-center gap-4">
                        <label className="flex items-center space-x-2 cursor-pointer text-sm text-brand-secondary whitespace-nowrap">
                            <input
                                type="checkbox"
                                checked={showPaid}
                                onChange={(e) => setShowPaid(e.target.checked)}
                                className="h-4 w-4 rounded border-brand-border bg-brand-bg-alt text-brand-secondary focus:ring-brand-secondary focus:ring-offset-gray-50"
                            />
                            <span>Show Paid</span>
                        </label>
                        <button
                            onClick={handleAddBill}
                            className="inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-brand-primary hover:bg-brand-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary transition-transform transform hover:scale-105"
                        >
                            <PlusIcon className="w-4 h-4 mr-2" />
                            Add Bill
                        </button>
                    </div>
                </header>
                <div className="p-4 space-y-3">
                    {displayedBills.length > 0 ? (
                        displayedBills.map(bill => {
                            const account = accounts.find(a => a.id === bill.accountId);
                            if (!account) return null;
                            const lastPaidBill = lastPaidBillsByAccount.get(bill.accountId);
                            return <BillRow key={bill.id} bill={bill} account={account} onEdit={handleEditPayment} onPay={handlePayBill} lastPaidBill={lastPaidBill} />;
                        })
                    ) : (
                         <p className="text-center text-brand-secondary py-6">
                            {showPaid ? "No bills to display." : "You're all caught up! No upcoming bills."}
                        </p>
                    )}
                </div>
            </div>

            {isBillFormModalOpen && (
                <BillFormModal 
                    isOpen={isBillFormModalOpen}
                    onClose={handleCloseModals}
                    billToEdit={billForForm}
                    accounts={accounts}
                />
            )}
            {isEditPaymentModalOpen && billToEditPayment && (
                <EditPaymentModal
                    isOpen={isEditPaymentModalOpen}
                    onClose={handleCloseModals}
                    bill={billToEditPayment}
                />
            )}
            {billToPay && (
                <PayBillModal
                    isOpen={!!billToPay}
                    onClose={handleCloseModals}
                    bill={billToPay.bill}
                    account={billToPay.account}
                />
            )}
        </>
    );
};

export default UpcomingBills;