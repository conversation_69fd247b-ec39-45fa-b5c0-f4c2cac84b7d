import React, { useState, useEffect, useContext } from 'react';
import { Account, AccountType, PaymentMethod } from '../../types';
import { BillsContext } from '../../contexts/BillsContext';
import { XMarkIcon } from '../icons/Icons';

interface AccountFormModalProps {
    isOpen: boolean;
    onClose: () => void;
    accountToEdit?: Account;
}

const INSTITUTION_SUGGESTIONS = ['Chase', 'American Express', 'Discover', 'Citi', 'Wells Fargo', 'Bank of America', 'Capital One', 'US Bank', 'Toyota Financial', 'Aidvantage'];

const AccountFormModal: React.FC<AccountFormModalProps> = ({ isOpen, onClose, accountToEdit }) => {
    const [name, setName] = useState('');
    const [type, setType] = useState<AccountType>(AccountType.CreditCard);
    const [balance, setBalance] = useState('');
    const [creditLimit, setCreditLimit] = useState('');
    const [institution, setInstitution] = useState('');
    const [lastFour, setLastFour] = useState('');
    const [paymentDay, setPaymentDay] = useState('');
    const [defaultPaymentMethod, setDefaultPaymentMethod] = useState<PaymentMethod>('Manual');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const billsContext = useContext(BillsContext);

    useEffect(() => {
        if (accountToEdit) {
            setName(accountToEdit.name);
            setType(accountToEdit.type);
            setBalance(String(accountToEdit.balance));
            setCreditLimit(String(accountToEdit.creditLimit || ''));
            setInstitution(accountToEdit.institution || '');
            setLastFour(accountToEdit.lastFourDigits || '');
            setPaymentDay(String(accountToEdit.paymentDay || ''));
            setDefaultPaymentMethod(accountToEdit.defaultPaymentMethod || 'Manual');
        } else {
            setName('');
            setType(AccountType.CreditCard);
            setBalance('');
            setCreditLimit('');
            setInstitution('');
            setLastFour('');
            setPaymentDay('');
            setDefaultPaymentMethod('Manual');
        }
        setIsSubmitting(false);
    }, [accountToEdit, isOpen]);

    if (!isOpen || !billsContext) return null;

    const { addAccount, updateAccount, deleteAccount } = billsContext;

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (isSubmitting) return;

        if (!name.trim() || !balance.trim()) {
            alert('Name and Balance are required.');
            return;
        }
        
        setIsSubmitting(true);

        const parsedBalance = parseFloat(balance);
        const parsedCreditLimit = creditLimit ? parseFloat(creditLimit) : undefined;
        const day = parseInt(paymentDay, 10);

        const accountData = {
            name,
            type,
            balance: isNaN(parsedBalance) ? 0 : parsedBalance,
            creditLimit: isNaN(parsedCreditLimit as number) ? undefined : parsedCreditLimit,
            institution,
            lastFourDigits: lastFour,
            paymentDay: !isNaN(day) && day >= 1 && day <= 31 ? day : undefined,
            defaultPaymentMethod: defaultPaymentMethod,
        };

        try {
            if (accountToEdit) {
                await updateAccount({ ...accountToEdit, ...accountData });
            } else {
                await addAccount(accountData);
            }
            onClose();
        } catch (error) {
            console.error("Failed to submit account:", error);
            setIsSubmitting(false);
        }
    };
    
    const handleDelete = async () => {
        if (isSubmitting) return;
        if (accountToEdit && window.confirm("Are you sure you want to permanently delete this account and all its associated bills?")) {
            setIsSubmitting(true);
            try {
                await deleteAccount(accountToEdit.id);
                onClose();
            } catch (error) {
                console.error("Failed to delete account:", error);
                setIsSubmitting(false);
            }
        }
    };

    const inputBaseClasses = "mt-1 block w-full rounded-md border-brand-border bg-brand-bg-alt text-brand-primary shadow-sm focus:border-brand-secondary focus:ring-brand-secondary sm:text-sm transition";
    
    return (
        <div className="fixed inset-0 bg-brand-primary/20 backdrop-blur-sm flex justify-center items-center z-50 p-4 animate-fade-in" role="dialog" aria-modal="true">
            <div className="bg-white border border-brand-border rounded-lg shadow-xl w-full max-w-lg max-h-[90vh] flex flex-col transform animate-zoom-in">
                <header className="flex items-center justify-between p-5 border-b border-brand-border">
                    <h2 className="text-xl font-bold text-brand-primary">{accountToEdit ? 'Edit Account' : 'Add New Account'}</h2>
                    <button onClick={onClose} className="p-1 rounded-full text-brand-secondary hover:bg-brand-bg-alt focus:outline-none focus:ring-2 focus:ring-brand-secondary">
                        <XMarkIcon className="w-6 h-6" />
                    </button>
                </header>
                <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto custom-scrollbar p-6 space-y-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <div>
                            <label htmlFor="name" className="block text-sm font-medium text-brand-secondary">Account Name</label>
                            <input type="text" id="name" value={name} onChange={e => setName(e.target.value)} required className={inputBaseClasses} placeholder="e.g., Chase Sapphire" />
                        </div>
                        <div>
                            <label htmlFor="type" className="block text-sm font-medium text-brand-secondary">Account Type</label>
                            <select id="type" value={type} onChange={e => setType(e.target.value as AccountType)} className={inputBaseClasses}>
                                {Object.values(AccountType).map(t => <option key={t} value={t}>{t}</option>)}
                            </select>
                        </div>
                    </div>
                    <div>
                        <label htmlFor="balance" className="block text-sm font-medium text-brand-secondary">Current Balance</label>
                        <div className="relative mt-1">
                            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"><span className="text-brand-secondary sm:text-sm">$</span></div>
                            <input type="number" id="balance" value={balance} onChange={e => setBalance(e.target.value)} required className={`${inputBaseClasses} pl-7`} placeholder="0.00" step="0.01" />
                        </div>
                    </div>
                     {type === AccountType.CreditCard && (
                        <div>
                            <label htmlFor="creditLimit" className="block text-sm font-medium text-brand-secondary">Credit Limit (Optional)</label>
                            <div className="relative mt-1">
                                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"><span className="text-brand-secondary sm:text-sm">$</span></div>
                                <input type="number" id="creditLimit" value={creditLimit} onChange={e => setCreditLimit(e.target.value)} className={`${inputBaseClasses} pl-7`} placeholder="5000" step="0.01" />
                            </div>
                        </div>
                    )}
                     <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <div>
                            <label htmlFor="institution" className="block text-sm font-medium text-brand-secondary">Institution</label>
                             <input type="text" id="institution" value={institution} onChange={e => setInstitution(e.target.value)} list="institution-suggestions" className={inputBaseClasses} placeholder="e.g., Chase" />
                            <datalist id="institution-suggestions">
                                {INSTITUTION_SUGGESTIONS.map(cat => <option key={cat} value={cat} />)}
                            </datalist>
                        </div>
                        <div>
                            <label htmlFor="lastFour" className="block text-sm font-medium text-brand-secondary">Last 4 Digits</label>
                            <input type="text" id="lastFour" value={lastFour} onChange={e => setLastFour(e.target.value)} className={inputBaseClasses} maxLength={4} placeholder="1234" />
                        </div>
                    </div>
                    
                    <div className="p-4 bg-gray-50 rounded-lg">
                        <h3 className="text-sm font-medium text-brand-secondary mb-3">Default Payment Settings (Optional)</h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                             <div>
                                <label htmlFor="paymentDay" className="block text-sm font-medium text-brand-secondary">Typical Payment Day</label>
                                <input type="number" id="paymentDay" value={paymentDay} onChange={e => setPaymentDay(e.target.value)} className={inputBaseClasses} min="1" max="31" placeholder="e.g., 15" />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-brand-secondary">Default Method</label>
                                <div className="mt-2 flex gap-4">
                                    <label className="flex items-center">
                                        <input type="radio" name="defaultPaymentMethod" value="Manual" checked={defaultPaymentMethod === 'Manual'} onChange={() => setDefaultPaymentMethod('Manual')} className="h-4 w-4 text-brand-secondary focus:ring-brand-secondary border-brand-border" />
                                        <span className="ml-2 text-sm text-brand-primary">Manual</span>
                                    </label>
                                    <label className="flex items-center">
                                        <input type="radio" name="defaultPaymentMethod" value="Auto Pay" checked={defaultPaymentMethod === 'Auto Pay'} onChange={() => setDefaultPaymentMethod('Auto Pay')} className="h-4 w-4 text-brand-secondary focus:ring-brand-secondary border-brand-border" />
                                        <span className="ml-2 text-sm text-brand-primary">Auto Pay</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>


                </form>
                <footer className="flex justify-between items-center p-4 bg-gray-50 border-t border-brand-border">
                    <div>
                        {accountToEdit && (
                            <button
                                type="button"
                                onClick={handleDelete}
                                disabled={isSubmitting}
                                className="px-4 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-500/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 focus:ring-offset-gray-50 transition-colors disabled:opacity-50"
                            >
                                Delete Account
                            </button>
                        )}
                    </div>
                    <div className="space-x-3">
                        <button type="button" onClick={onClose} disabled={isSubmitting} className="px-4 py-2 border border-brand-border text-sm font-medium rounded-md shadow-sm text-brand-primary bg-white hover:bg-brand-bg-alt focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-gray-50 disabled:opacity-50">Cancel</button>
                        <button type="submit" onClick={handleSubmit} disabled={isSubmitting} className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-brand-primary hover:bg-brand-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-gray-50 disabled:opacity-50">{accountToEdit ? 'Save Changes' : 'Add Account'}</button>
                    </div>
                </footer>
            </div>
        </div>
    );
};

export default AccountFormModal;