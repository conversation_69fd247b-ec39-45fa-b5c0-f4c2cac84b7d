import React from 'react';
import { Bill, Account } from '../../types';
import { formatBillDueDate } from '../../utils/dateUtils';
import { PencilIcon } from '../icons/Icons';

interface BillRowProps {
    bill: Bill;
    account: Account;
    onEdit: (bill: Bill) => void;
    onPay: (bill: Bill, account: Account) => void;
    lastPaidBill?: Bill;
}

const formatCurrency = (value: number) => new Intl.NumberFormat('en-US', { style: 'currency', 'currency': 'USD' }).format(value);

const LastPaymentInfo: React.FC<{ lastPaidBill?: Bill }> = ({ lastPaidBill }) => {
    if (lastPaidBill && lastPaidBill.paidDate && typeof lastPaidBill.paidAmount === 'number') {
        const lastPaidDateFormatted = new Date(lastPaidBill.paidDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        return (
            <p className="text-xs text-brand-secondary mt-0.5">
                Last paid: {lastPaidDateFormatted} for {formatCurrency(lastPaidBill.paidAmount)}
            </p>
        );
    }
    return <p className="text-xs text-brand-secondary mt-0.5">Never paid</p>;
};

const BillRow: React.FC<BillRowProps> = ({ bill, account, onEdit, onPay, lastPaidBill }) => {
    
    if (bill.isPaid) {
        const paidDateFormatted = bill.paidDate ? new Date(bill.paidDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) : 'N/A';

        return (
            <div className="bg-gray-50/70 border border-brand-border rounded-lg p-3 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 opacity-70">
                <div className="flex items-center flex-1">
                    <div className="w-5 h-5 flex-shrink-0 mr-4 flex items-center justify-center">
                        <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <div>
                        <p className="font-semibold text-brand-primary">{account.name}</p>
                        <p className="text-sm text-brand-secondary">{account.institution}</p>
                    </div>
                </div>

                <div className="pl-9 sm:pl-0 flex-1 grid grid-cols-2 sm:grid-cols-3 gap-4 text-sm w-full">
                    <div className="text-left">
                        <p className="font-semibold text-green-700">Paid on {paidDateFormatted}</p>
                    </div>

                    <div className="text-left sm:text-right">
                        <p className="font-semibold text-brand-primary">{formatCurrency(bill.paidAmount || 0)}</p>
                    </div>
                    
                    <div className="col-span-2 sm:col-span-1 sm:text-right">
                         <span className={`px-2 py-1 rounded-full text-xs font-medium bg-gray-200 text-gray-800`}>
                            {bill.paymentMethod}
                        </span>
                    </div>
                </div>

                <div className="pl-9 sm:pl-0">
                     <button 
                        onClick={() => onEdit(bill)}
                        className="p-1.5 text-brand-secondary hover:text-brand-primary hover:bg-brand-bg-alt rounded-md focus:outline-none focus:ring-2 focus:ring-brand-secondary"
                        aria-label={`Edit bill for ${account.name}`}
                    >
                        <PencilIcon className="w-5 h-5" />
                    </button>
                </div>
            </div>
        );
    }
    
    // Unpaid Bill View
    const { text: dueDateText, isOverdue } = formatBillDueDate(bill.dueDate);
    const date = new Date(bill.dueDate);
    date.setMinutes(date.getMinutes() + date.getTimezoneOffset());
    const formattedDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

    return (
        <div className="bg-gray-50/70 border border-brand-border rounded-lg p-3 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-center flex-1">
                 <div className="w-5 h-5 flex-shrink-0 mr-4 border-2 border-zinc-300 rounded-full" />
                <div className="flex-1">
                    <p className="font-semibold text-brand-primary">{account.name}</p>
                    <p className="text-sm text-brand-secondary">{account.institution}</p>
                    <LastPaymentInfo lastPaidBill={lastPaidBill} />
                </div>
            </div>
            
            <div className="pl-9 sm:pl-0 flex-1 grid grid-cols-2 sm:grid-cols-3 gap-4 text-sm w-full">
                <div className="text-left">
                    <p className={`font-semibold ${isOverdue ? 'text-rose-600' : 'text-brand-primary'}`}>{dueDateText}</p>
                    <p className="text-brand-secondary">{formattedDate}</p>
                </div>

                <div className="text-left sm:text-right">
                    <p className="font-semibold text-brand-primary">{formatCurrency(bill.statementBalance)}</p>
                    <p className="text-brand-secondary">Min: {formatCurrency(bill.minPayment)}</p>
                </div>
                
                <div className="col-span-2 sm:col-span-1 sm:text-right">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${bill.paymentMethod === 'Auto Pay' ? 'bg-blue-100 text-blue-800' : 'bg-gray-200 text-gray-800'}`}>
                        {bill.paymentMethod}
                    </span>
                </div>
            </div>
            
            <div className="pl-9 sm:pl-0 flex items-center gap-2">
                 <button
                    onClick={() => onPay(bill, account)}
                    className="px-3 py-1.5 text-sm font-medium rounded-md text-white bg-brand-primary hover:bg-brand-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary transition-colors"
                >
                    Pay Bill
                </button>
                 <button 
                    onClick={() => onEdit(bill)}
                    className="p-1.5 text-brand-secondary hover:text-brand-primary hover:bg-brand-bg-alt rounded-md focus:outline-none focus:ring-2 focus:ring-brand-secondary"
                    aria-label={`Edit bill for ${account.name}`}
                >
                    <PencilIcon className="w-5 h-5" />
                </button>
            </div>
        </div>
    );
};

export default BillRow;