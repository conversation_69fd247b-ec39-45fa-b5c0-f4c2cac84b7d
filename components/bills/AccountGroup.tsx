import React, { useState, useMemo } from 'react';
import { Account, Bill, AccountType } from '../../types';
import { ChevronDownIcon } from '../icons/Icons';
import AccountCard from './AccountCard';
import BillFormModal from './BillFormModal';
import EditPaymentModal from './EditPaymentModal';

interface AccountGroupProps {
    title: string;
    type: AccountType;
    accounts: Account[];
    allAccounts: Account[];
    bills: Bill[];
}

const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
};

const AccountGroup: React.FC<AccountGroupProps> = ({ title, type, accounts, allAccounts, bills }) => {
    const [isOpen, setIsOpen] = useState(true);
    const [isBillModalOpen, setIsBillModalOpen] = useState(false);
    const [billToEdit, setBillToEdit] = useState<Bill | undefined>(undefined);
    const [isEditPaymentModalOpen, setIsEditPaymentModalOpen] = useState(false);
    const [billToEditPayment, setBillToEditPayment] = useState<Bill | null>(null);


    const totalBalance = useMemo(() => {
        return accounts.reduce((sum, acc) => sum + acc.balance, 0);
    }, [accounts]);

    const handleEditPayment = (bill: Bill) => {
        setBillToEditPayment(bill);
        setIsEditPaymentModalOpen(true);
    };

    const handleCloseModal = () => {
        setIsBillModalOpen(false);
        setBillToEdit(undefined);
        setIsEditPaymentModalOpen(false);
        setBillToEditPayment(null);
    };

    return (
        <>
            <div className="bg-white rounded-xl shadow-sm border border-brand-border">
                <button 
                    className="flex items-center justify-between w-full p-4 text-left"
                    onClick={() => setIsOpen(!isOpen)}
                    aria-expanded={isOpen}
                >
                    <div className="flex items-center">
                         <ChevronDownIcon className={`w-5 h-5 text-brand-secondary mr-3 transition-transform duration-200 ${isOpen ? '' : '-rotate-90'}`} />
                        <h3 className="text-lg font-bold text-brand-primary">{title}</h3>
                    </div>
                    <div className="text-right">
                        <p className="text-lg font-bold text-brand-primary">{formatCurrency(totalBalance)}</p>
                        {/* Placeholder for change info */}
                    </div>
                </button>
                {isOpen && (
                    <div className="px-4 pb-4 space-y-3">
                        {accounts.length > 0 ? (
                            accounts
                                .sort((a,b) => b.balance - a.balance)
                                .map(account => <AccountCard key={account.id} account={account} bills={bills} onEditBill={handleEditPayment} />)
                        ) : (
                            <p className="text-center text-brand-secondary py-4">No {title.toLowerCase()} accounts added yet.</p>
                        )}
                    </div>
                )}
            </div>

            {isBillModalOpen && (
                <BillFormModal 
                    isOpen={isBillModalOpen}
                    onClose={handleCloseModal}
                    billToEdit={billToEdit}
                    accounts={allAccounts}
                />
            )}
            
            {isEditPaymentModalOpen && billToEditPayment && (
                 <EditPaymentModal
                    isOpen={isEditPaymentModalOpen}
                    onClose={handleCloseModal}
                    bill={billToEditPayment}
                />
            )}
        </>
    );
};

export default AccountGroup;