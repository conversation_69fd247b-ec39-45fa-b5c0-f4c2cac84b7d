
import React, { useMemo, useState } from 'react';
import { Account, AccountType } from '../../types';

interface SummaryWidgetProps {
    accounts: Account[];
}

const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
};

const formatPercent = (value: number) => {
    return `${value.toFixed(1)}%`;
}

const Bar: React.FC<{ segments: { value: number; color: string; label: string }[] }> = ({ segments }) => {
    const total = segments.reduce((acc, seg) => acc + seg.value, 0);
    if (total === 0) return <div className="h-3 bg-gray-200 rounded-full"></div>;

    return (
        <div className="flex h-3 rounded-full overflow-hidden bg-gray-200">
            {segments.map((seg, i) => (
                <div
                    key={i}
                    className={seg.color}
                    style={{ width: `${(seg.value / total) * 100}%` }}
                    title={`${seg.label}: ${formatCurrency(seg.value)}`}
                />
            ))}
        </div>
    );
};

const SummaryWidget: React.FC<SummaryWidgetProps> = ({ accounts }) => {
    const [displayMode, setDisplayMode] = useState<'totals' | 'percent'>('totals');

    const summary = useMemo(() => {
        const liabilities = {
            creditCards: accounts.filter(a => a.type === AccountType.CreditCard).reduce((sum, acc) => sum + acc.balance, 0),
            loans: accounts.filter(a => a.type === AccountType.Loan).reduce((sum, acc) => sum + acc.balance, 0),
        };
        const totalLiabilities = Object.values(liabilities).reduce((sum, val) => sum + val, 0);

        return { liabilities, totalLiabilities };
    }, [accounts]);
    
    const ToggleButton: React.FC<{ label: string, mode: 'totals' | 'percent' }> = ({ label, mode }) => (
        <button
            onClick={() => setDisplayMode(mode)}
            className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                displayMode === mode
                    ? 'bg-brand-primary text-white shadow'
                    : 'text-brand-secondary hover:bg-brand-bg-alt'
            }`}
        >
            {label}
        </button>
    );

    return (
        <div className="bg-white rounded-xl shadow-sm border border-brand-border p-5">
            <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-bold text-brand-primary">Liabilities Summary</h2>
                <div className="flex items-center space-x-1 bg-brand-bg-alt p-1 rounded-lg">
                   <ToggleButton label="Totals" mode="totals" />
                   <ToggleButton label="Percent" mode="percent" />
                </div>
            </div>

            <div className="space-y-6">
                <div>
                    <div className="flex justify-between items-baseline mb-2">
                        <h3 className="font-semibold text-brand-primary">Total Liabilities</h3>
                        <p className="font-bold text-lg text-rose-600">{formatCurrency(summary.totalLiabilities)}</p>
                    </div>
                    <Bar segments={[
                        { value: summary.liabilities.loans, color: 'bg-yellow-400', label: 'Loans' },
                        { value: summary.liabilities.creditCards, color: 'bg-rose-500', label: 'Credit Cards' },
                    ]} />
                    <div className="mt-3 space-y-2 text-sm">
                        <div className="flex items-center">
                            <span className="w-3 h-3 rounded-full bg-yellow-400 mr-2"></span>
                            <span className="text-brand-secondary">Loans</span>
                            <span className="ml-auto font-medium text-brand-primary">
                                {displayMode === 'totals' 
                                    ? formatCurrency(summary.liabilities.loans)
                                    : formatPercent(summary.totalLiabilities > 0 ? (summary.liabilities.loans / summary.totalLiabilities) * 100 : 0)
                                }
                            </span>
                        </div>
                        <div className="flex items-center">
                            <span className="w-3 h-3 rounded-full bg-rose-500 mr-2"></span>
                            <span className="text-brand-secondary">Credit Cards</span>
                            <span className="ml-auto font-medium text-brand-primary">
                                {displayMode === 'totals' 
                                    ? formatCurrency(summary.liabilities.creditCards)
                                    : formatPercent(summary.totalLiabilities > 0 ? (summary.liabilities.creditCards / summary.totalLiabilities) * 100 : 0)
                                }
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SummaryWidget;
