import React, { useState, useEffect, useContext } from 'react';
import { Task, Priority, RecurrenceType } from '../../types';
import { TaskContext } from '../../contexts/TaskContext';
import { XMarkIcon } from '../icons/Icons';

interface TaskFormModalProps {
    isOpen: boolean;
    onClose: () => void;
    taskToEdit?: Task;
}

const CATEGORY_SUGGESTIONS = ['Finance', 'Work', 'Personal', 'Health', 'Home', 'Bills', 'Travel', 'Shopping', 'Education', 'Errands'];

const TaskFormModal: React.FC<TaskFormModalProps> = ({ isOpen, onClose, taskToEdit }) => {
    const [title, setTitle] = useState('');
    const [description, setDescription] = useState('');
    const [dueDate, setDueDate] = useState('');
    const [dueTime, setDueTime] = useState('');
    const [category, setCategory] = useState('');
    const [priority, setPriority] = useState<Priority>(Priority.Medium);
    const [isRecurring, setIsRecurring] = useState(false);
    const [recurrenceInterval, setRecurrenceInterval] = useState(1);
    const [recurrenceType, setRecurrenceType] = useState<RecurrenceType>(RecurrenceType.Weekly);
    const [isSubmitting, setIsSubmitting] = useState(false);
    
    const taskContext = useContext(TaskContext);

    useEffect(() => {
        if (taskToEdit) {
            setTitle(taskToEdit.title);
            setDescription(taskToEdit.description || '');
            setDueDate(taskToEdit.dueDate);
            setDueTime(taskToEdit.dueTime);
            setCategory(taskToEdit.category || '');
            setPriority(taskToEdit.priority);
            setIsRecurring(taskToEdit.isRecurring);
            setRecurrenceType(taskToEdit.recurrenceType || RecurrenceType.Weekly);
            setRecurrenceInterval(taskToEdit.recurrenceInterval || 1);
        } else {
            // Reset to defaults for new task
            setTitle('');
            setDescription('');
            setDueDate(new Date().toISOString().split('T')[0]);
            setDueTime('09:00');
            setCategory('');
            setPriority(Priority.Medium);
            setIsRecurring(false);
            setRecurrenceType(RecurrenceType.Weekly);
            setRecurrenceInterval(1);
        }
        setIsSubmitting(false);
    }, [taskToEdit, isOpen]);

    if (!isOpen || !taskContext) return null;
    const { addTask, updateTask, deleteTask } = taskContext;

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (isSubmitting) return;

        if (!title.trim()) {
            alert('Title is required.');
            return;
        }
        
        setIsSubmitting(true);

        const taskData = {
            title,
            description,
            dueDate,
            dueTime,
            category,
            priority,
            isRecurring,
            recurrenceType: isRecurring ? recurrenceType : undefined,
            recurrenceInterval: isRecurring ? recurrenceInterval : undefined,
        };

        try {
            if (taskToEdit) {
                await updateTask({ ...taskToEdit, ...taskData });
            } else {
                await addTask(taskData);
            }
            onClose();
        } catch (error) {
            console.error("Failed to submit task:", error);
            setIsSubmitting(false); // Re-enable form on error
        }
    };

    const handleDelete = () => {
        if (taskToEdit && window.confirm("Are you sure you want to permanently delete this task? This action cannot be undone.")) {
            deleteTask(taskToEdit.id);
            onClose();
        }
    };

    const recurrenceLabel = recurrenceInterval === 1 ? recurrenceType?.toLowerCase().slice(0, -1) : `${recurrenceType?.toLowerCase().slice(0, -1)}s`;

    const inputBaseClasses = "mt-1 block w-full rounded-md border-brand-border bg-brand-bg-alt text-brand-primary shadow-sm focus:border-brand-secondary focus:ring-brand-secondary sm:text-sm transition";

    return (
        <div className="fixed inset-0 bg-brand-primary/20 backdrop-blur-sm flex justify-center items-center z-50 p-4 animate-fade-in" role="dialog" aria-modal="true">
            <div className="bg-white border border-brand-border rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col transform animate-zoom-in">
                <header className="flex items-center justify-between p-5 border-b border-brand-border">
                    <h2 className="text-xl font-bold text-brand-primary">{taskToEdit ? 'Edit Task' : 'Add New Task'}</h2>
                    <button onClick={onClose} className="p-1 rounded-full text-brand-secondary hover:bg-brand-bg-alt focus:outline-none focus:ring-2 focus:ring-brand-secondary">
                        <XMarkIcon className="w-6 h-6" />
                    </button>
                </header>
                <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto custom-scrollbar p-6 space-y-6">
                    <div>
                        <label htmlFor="title" className="block text-sm font-medium text-brand-secondary">Title</label>
                        <input type="text" id="title" value={title} onChange={e => setTitle(e.target.value)} required className={inputBaseClasses} />
                    </div>
                    <div>
                        <label htmlFor="description" className="block text-sm font-medium text-brand-secondary">Description</label>
                        <textarea id="description" value={description} onChange={e => setDescription(e.target.value)} rows={3} className={inputBaseClasses} />
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <div>
                            <label htmlFor="dueDate" className="block text-sm font-medium text-brand-secondary">Due Date</label>
                            <input type="date" id="dueDate" value={dueDate} onChange={e => setDueDate(e.target.value)} required className={inputBaseClasses} />
                        </div>
                        <div>
                            <label htmlFor="dueTime" className="block text-sm font-medium text-brand-secondary">Due Time</label>
                            <input type="time" id="dueTime" value={dueTime} onChange={e => setDueTime(e.target.value)} required className={inputBaseClasses} />
                        </div>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <div>
                            <label htmlFor="category" className="block text-sm font-medium text-brand-secondary">Category</label>
                            <input type="text" id="category" value={category} onChange={e => setCategory(e.target.value)} list="category-suggestions" className={inputBaseClasses} />
                            <datalist id="category-suggestions">
                                {CATEGORY_SUGGESTIONS.map(cat => <option key={cat} value={cat} />)}
                            </datalist>
                        </div>
                        <div>
                            <label htmlFor="priority" className="block text-sm font-medium text-brand-secondary">Priority</label>
                            <select id="priority" value={priority} onChange={e => setPriority(e.target.value as Priority)} className={inputBaseClasses}>
                                {Object.values(Priority).map(p => <option key={p} value={p}>{p}</option>)}
                            </select>
                        </div>
                    </div>
                    <div>
                        <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                            <input type="checkbox" id="isRecurring" checked={isRecurring} onChange={e => setIsRecurring(e.target.checked)} className="h-4 w-4 rounded border-brand-border bg-white text-brand-secondary focus:ring-brand-secondary" />
                            <label htmlFor="isRecurring" className="ml-3 block text-sm font-medium text-brand-primary">Recurring Task</label>
                        </div>
                    </div>
                    {isRecurring && (
                        <div className="p-4 bg-gray-50 rounded-md grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div>
                                <label htmlFor="recurrenceType" className="block text-sm font-medium text-brand-secondary">Repeats</label>
                                <select id="recurrenceType" value={recurrenceType} onChange={e => setRecurrenceType(e.target.value as RecurrenceType)} className={inputBaseClasses}>
                                    {Object.values(RecurrenceType).map(rt => <option key={rt} value={rt}>{rt}</option>)}
                                </select>
                            </div>
                            <div className="flex items-end space-x-2">
                                <div className="flex-1">
                                    <label htmlFor="recurrenceInterval" className="block text-sm font-medium text-brand-secondary">Every</label>
                                    <input type="number" id="recurrenceInterval" value={recurrenceInterval} onChange={e => setRecurrenceInterval(Math.max(1, parseInt(e.target.value, 10) || 1))} min="1" className={inputBaseClasses} />
                                </div>
                                <span className="pb-2 text-sm text-brand-secondary">{recurrenceLabel}</span>
                            </div>
                        </div>
                    )}
                </form>
                <footer className="flex justify-between items-center p-4 bg-gray-50 border-t border-brand-border">
                    <div>
                        {taskToEdit && (
                            <button
                                type="button"
                                onClick={handleDelete}
                                disabled={isSubmitting}
                                className="px-4 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-500/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 focus:ring-offset-gray-50 transition-colors disabled:opacity-50"
                            >
                                Delete Task
                            </button>
                        )}
                    </div>
                    <div className="space-x-3">
                        <button type="button" onClick={onClose} disabled={isSubmitting} className="px-4 py-2 border border-brand-border text-sm font-medium rounded-md shadow-sm text-brand-primary bg-white hover:bg-brand-bg-alt focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-gray-50 disabled:opacity-50">Cancel</button>
                        <button type="submit" onClick={handleSubmit} disabled={isSubmitting} className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-brand-primary hover:bg-brand-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-gray-50 disabled:opacity-50">{taskToEdit ? 'Save Changes' : 'Add Task'}</button>
                    </div>
                </footer>
            </div>
        </div>
    );
};

export default TaskFormModal;