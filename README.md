<div align="center">
<img width="1200" height="475" alt="GHBanner" src="https://github.com/user-attachments/assets/0aa67016-6eaf-458a-adb2-6e31a0763ed6" />
</div>

# Run and deploy your AI Studio app

This contains everything you need to run your app locally.

View your app in AI Studio: https://ai.studio/apps/drive/1lUICKeMl2EHKdpkfbyVIxHMkKfmizJBT

## Run Locally

**Prerequisites:**  Node.js


1. Install dependencies:
   `npm install`
2. Create `.env.local` and set:
   - `VITE_SUPABASE_URL=your_supabase_project_url`
   - `VITE_SUPABASE_ANON_KEY=your_supabase_anon_key`
   - (Optional) `GEMINI_API_KEY=...`
3. In Supabase: enable Email auth (no confirmations) and run `supabase/schema.sql` in the SQL Editor to create tables and RLS.
4. Run the app:
   `npm run dev`

## Deploy to Vercel

- Import the repo into Vercel and set environment variables in the Project Settings for both Preview and Production:
  - `VITE_SUPABASE_URL`
  - `VITE_SUPABASE_ANON_KEY`
- Framework preset: Vite (auto).
- Build command: `vite build`; Output directory: `dist`.
