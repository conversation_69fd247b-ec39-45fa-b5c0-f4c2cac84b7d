
import initSqlJs from 'sql.js';
import { Task, Settings, Priority, RecurrenceType, Account, AccountType, Bill, PaymentMethod, Connection, ContactFrequency } from '../types';

let db: initSqlJs.Database | null = null;
let SQL: initSqlJs.SqlJsStatic;

const DB_STORAGE_KEY = 'lifetracker_sqlite_db';

async function initDB() {
    if (db) return db;

    try {
        const wasmResponse = await fetch('https://cdn.jsdelivr.net/npm/sql.js@1.13.0/dist/sql-wasm.wasm');
        const wasmBinary = await wasmResponse.arrayBuffer();
        SQL = await initSqlJs({ wasmBinary });

        const storedDb = localStorage.getItem(DB_STORAGE_KEY);
        if (storedDb) {
            const dbArray = new Uint8Array(JSON.parse(storedDb));
            db = new SQL.Database(dbArray);
        } else {
            db = new SQL.Database();
            createSchema();
        }
    } catch (e) {
        console.error("Failed to initialize database:", e);
    }

    return db;
}

function saveDB() {
    if (db) {
        try {
            const data = db.export();
            localStorage.setItem(DB_STORAGE_KEY, JSON.stringify(Array.from(data)));
        } catch (e) {
            console.error("Failed to save database:", e);
        }
    }
}

function createSchema() {
    if (!db) return;

    const schema = `
        CREATE TABLE IF NOT EXISTS tasks (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT,
            dueDate TEXT NOT NULL,
            dueTime TEXT NOT NULL,
            isCompleted INTEGER NOT NULL,
            completedAt TEXT,
            priority TEXT NOT NULL,
            category TEXT,
            isRecurring INTEGER NOT NULL,
            recurrenceType TEXT,
            recurrenceInterval INTEGER,
            createdAt TEXT NOT NULL
        );

        CREATE TABLE IF NOT EXISTS settings (
            key TEXT PRIMARY KEY,
            value TEXT NOT NULL
        );

        CREATE TABLE IF NOT EXISTS accounts (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            balance REAL NOT NULL,
            creditLimit REAL,
            apr REAL,
            institution TEXT,
            lastFourDigits TEXT,
            paymentDay INTEGER,
            defaultPaymentMethod TEXT,
            createdAt TEXT NOT NULL
        );

        CREATE TABLE IF NOT EXISTS bills (
            id TEXT PRIMARY KEY,
            accountId TEXT NOT NULL,
            dueDate TEXT NOT NULL,
            minPayment REAL NOT NULL,
            statementBalance REAL NOT NULL,
            paidAmount REAL,
            paidDate TEXT,
            isPaid INTEGER NOT NULL,
            paymentMethod TEXT NOT NULL,
            createdAt TEXT NOT NULL,
            notes TEXT,
            extraPrincipal REAL,
            FOREIGN KEY (accountId) REFERENCES accounts (id) ON DELETE CASCADE
        );

        CREATE TABLE IF NOT EXISTS connections (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            role TEXT,
            company TEXT,
            lastContactDate TEXT NOT NULL,
            contactFrequency TEXT NOT NULL,
            createdAt TEXT NOT NULL
        );
    `;
    db.exec(schema);

    db.run("INSERT OR IGNORE INTO settings (key, value) VALUES ('showCompleted', 'true'), ('seeded', 'false'), ('billsSeeded', 'false'), ('connectionsSeeded', 'false');");
    
    saveDB();
}

function rowToTask(row: (string | number | null)[]): Task {
    return {
        id: row[0] as string,
        title: row[1] as string,
        description: row[2] as string | undefined,
        dueDate: row[3] as string,
        dueTime: row[4] as string,
        isCompleted: row[5] === 1,
        completedAt: row[6] as string | undefined,
        priority: row[7] as Priority,
        category: row[8] as string | undefined,
        isRecurring: row[9] === 1,
        recurrenceType: row[10] as RecurrenceType | undefined,
        recurrenceInterval: row[11] as number | undefined,
        createdAt: row[12] as string,
    };
}

function rowToAccount(row: (string | number | null)[]): Account {
    return {
        id: row[0] as string,
        name: row[1] as string,
        type: row[2] as AccountType,
        balance: row[3] as number,
        creditLimit: row[4] as number | undefined,
        apr: row[5] as number | undefined,
        institution: row[6] as string | undefined,
        lastFourDigits: row[7] as string | undefined,
        paymentDay: row[8] as number | undefined,
        defaultPaymentMethod: row[9] as PaymentMethod | undefined,
        createdAt: row[10] as string,
    };
}

function rowToBill(row: (string | number | null)[]): Bill {
    return {
        id: row[0] as string,
        accountId: row[1] as string,
        dueDate: row[2] as string,
        minPayment: row[3] as number,
        statementBalance: row[4] as number,
        paidAmount: row[5] as number | undefined,
        paidDate: row[6] as string | undefined,
        isPaid: row[7] === 1,
        paymentMethod: row[8] as PaymentMethod,
        createdAt: row[9] as string,
        notes: row[10] as string | undefined,
        extraPrincipal: row[11] as number | undefined,
    };
}

function rowToConnection(row: (string | number | null)[]): Connection {
    return {
        id: row[0] as string,
        name: row[1] as string,
        role: row[2] as string | undefined,
        company: row[3] as string | undefined,
        lastContactDate: row[4] as string,
        contactFrequency: row[5] as ContactFrequency,
        createdAt: row[6] as string,
    };
}

export const dbService = {
    isInitialized: false,
    _inTransaction: false,

    async initialize(): Promise<void> {
        if(this.isInitialized) return;
        await initDB();
        this.isInitialized = true;
    },

    clearDatabase(): void {
        localStorage.removeItem(DB_STORAGE_KEY);
        db = null;
        this.isInitialized = false;
    },

    async runInTransaction(callback: () => Promise<void>): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for transaction.");

        if (this._inTransaction) {
            // Already in a transaction, just run the callback.
            await callback();
            return;
        }
        
        db.exec("BEGIN TRANSACTION;");
        this._inTransaction = true;
        try {
            await callback();
            db.exec("COMMIT;");
        } catch (e) {
            console.error("Transaction failed, rolling back", e);
            db.exec("ROLLBACK;");
            throw e;
        } finally {
            this._inTransaction = false;
            saveDB();
        }
    },

    async getSettings(): Promise<Settings & { billsSeeded?: boolean; connectionsSeeded?: boolean }> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for getSettings.");
        const res = db.exec("SELECT key, value FROM settings");
        const settings: Partial<Settings & { billsSeeded?: boolean; connectionsSeeded?: boolean }> = {
            showCompleted: true,
            seeded: false,
            billsSeeded: false,
            connectionsSeeded: false,
        };
        if (res[0]) {
            res[0].values.forEach(([key, value]) => {
                const k = key as keyof typeof settings;
                if (k === 'showCompleted' || k === 'seeded' || k === 'billsSeeded' || k === 'connectionsSeeded') {
                    settings[k] = value === 'true';
                }
            });
        }
        return settings as Settings & { billsSeeded?: boolean; connectionsSeeded?: boolean };
    },

    async updateSetting(key: keyof Settings | 'billsSeeded' | 'connectionsSeeded', value: string | boolean): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for updateSetting.");
        db.run("INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)", [key, String(value)]);
        if (!this._inTransaction) saveDB();
    },

    // Task Methods
    async getTasks(): Promise<Task[]> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for getTasks.");
        const res = db.exec("SELECT * FROM tasks");
        if (res.length === 0 || !res[0]?.values) return [];
        return res[0].values.map(row => rowToTask(row as any[]));
    },

    async addFullTask(task: Task): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for addFullTask.");
        const sql = `INSERT INTO tasks (id, title, description, dueDate, dueTime, isCompleted, completedAt, priority, category, isRecurring, recurrenceType, recurrenceInterval, createdAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
        const params = [
            task.id, task.title, task.description ?? null, task.dueDate, task.dueTime,
            task.isCompleted ? 1 : 0, task.completedAt ?? null, task.priority, task.category ?? null,
            task.isRecurring ? 1 : 0, task.recurrenceType ?? null, task.recurrenceInterval ?? null, task.createdAt
        ];
        db.run(sql, params);
        if (!this._inTransaction) saveDB();
    },

    async addTask(task: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>): Promise<Task> {
        const newTask: Task = { ...task, id: crypto.randomUUID(), isCompleted: false, completedAt: undefined, createdAt: new Date().toISOString() };
        await this.addFullTask(newTask);
        return newTask;
    },
    
    async updateTask(task: Task): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for updateTask.");
        db.run(`UPDATE tasks SET title = ?, description = ?, dueDate = ?, dueTime = ?, isCompleted = ?, completedAt = ?, priority = ?, category = ?, isRecurring = ?, recurrenceType = ?, recurrenceInterval = ? WHERE id = ?`, [
            task.title, task.description ?? null, task.dueDate, task.dueTime,
            task.isCompleted ? 1 : 0, task.completedAt ?? null, task.priority, task.category ?? null,
            task.isRecurring ? 1 : 0, task.recurrenceType ?? null, task.recurrenceInterval ?? null,
            task.id
        ]);
        if (!this._inTransaction) saveDB();
    },

    async deleteTask(taskId: string): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for deleteTask.");
        db.run("DELETE FROM tasks WHERE id = ?", [taskId]);
        if (!this._inTransaction) saveDB();
    },
    
    async seedInitialTasks(tasksToSeed: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>[]): Promise<void> {
        await this.initialize();
        const settings = await this.getSettings();
        if (settings.seeded) return;
        const tasks = await this.getTasks();
        if (tasks.length > 0) {
             await this.updateSetting('seeded', true);
             return;
        }
        await this.runInTransaction(async () => {
            for (const task of tasksToSeed) {
                await this.addTask(task);
            }
            await this.updateSetting('seeded', true);
        });
    },

    // Bills Methods
    async getAccounts(): Promise<Account[]> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for getAccounts.");
        const res = db.exec("SELECT * FROM accounts");
        if (res.length === 0 || !res[0]?.values) return [];
        return res[0].values.map(row => rowToAccount(row as any[]));
    },

    async addAccount(account: Omit<Account, 'id' | 'createdAt'>): Promise<Account> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for addAccount.");
        const newAccount: Account = { ...account, id: crypto.randomUUID(), createdAt: new Date().toISOString() };
        db.run(`INSERT INTO accounts (id, name, type, balance, creditLimit, apr, institution, lastFourDigits, paymentDay, defaultPaymentMethod, createdAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
            newAccount.id, newAccount.name, newAccount.type, newAccount.balance, newAccount.creditLimit ?? null, newAccount.apr ?? null, newAccount.institution ?? null, newAccount.lastFourDigits ?? null, newAccount.paymentDay ?? null, newAccount.defaultPaymentMethod ?? null, newAccount.createdAt
        ]);
        if (!this._inTransaction) saveDB();
        return newAccount;
    },

    async updateAccount(account: Account): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for updateAccount.");
        db.run(`UPDATE accounts SET name = ?, type = ?, balance = ?, creditLimit = ?, apr = ?, institution = ?, lastFourDigits = ?, paymentDay = ?, defaultPaymentMethod = ? WHERE id = ?`, [
            account.name, account.type, account.balance, account.creditLimit ?? null, account.apr ?? null, account.institution ?? null, account.lastFourDigits ?? null, account.paymentDay ?? null, account.defaultPaymentMethod ?? null, account.id
        ]);
        if (!this._inTransaction) saveDB();
    },

    async deleteAccount(accountId: string): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for deleteAccount.");
        db.run("DELETE FROM accounts WHERE id = ?", [accountId]);
        db.run("DELETE FROM bills WHERE accountId = ?", [accountId]); // Cascade delete
        if (!this._inTransaction) saveDB();
    },

    async getBills(): Promise<Bill[]> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for getBills.");
        const res = db.exec("SELECT * FROM bills");
        if (res.length === 0 || !res[0]?.values) return [];
        return res[0].values.map(row => rowToBill(row as any[]));
    },

    async addFullBill(bill: Bill): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for addFullBill.");
        db.run(`INSERT INTO bills (id, accountId, dueDate, minPayment, statementBalance, paidAmount, paidDate, isPaid, paymentMethod, createdAt, notes, extraPrincipal) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
            bill.id, bill.accountId, bill.dueDate, bill.minPayment, bill.statementBalance, bill.paidAmount ?? null, bill.paidDate ?? null, bill.isPaid ? 1 : 0, bill.paymentMethod, bill.createdAt, bill.notes ?? null, bill.extraPrincipal ?? null
        ]);
        if (!this._inTransaction) saveDB();
    },

    async addBill(bill: Omit<Bill, 'id' | 'createdAt' | 'isPaid'>): Promise<Bill> {
        const newBill: Bill = { ...bill, id: crypto.randomUUID(), isPaid: false, createdAt: new Date().toISOString() };
        await this.addFullBill(newBill);
        return newBill;
    },

    async updateBill(bill: Bill): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for updateBill.");
        db.run(`UPDATE bills SET accountId = ?, dueDate = ?, minPayment = ?, statementBalance = ?, paidAmount = ?, paidDate = ?, isPaid = ?, paymentMethod = ?, notes = ?, extraPrincipal = ? WHERE id = ?`, [
            bill.accountId, bill.dueDate, bill.minPayment, bill.statementBalance, bill.paidAmount ?? null, bill.paidDate ?? null, bill.isPaid ? 1 : 0, bill.paymentMethod, bill.notes ?? null, bill.extraPrincipal ?? null, bill.id
        ]);
        if (!this._inTransaction) saveDB();
    },

    async updateBillAndAccount(bill: Bill, newBalance: number): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for transaction.");

        await this.runInTransaction(async () => {
            const dbInstance = db as initSqlJs.Database;
            dbInstance.run(`UPDATE bills SET accountId = ?, dueDate = ?, minPayment = ?, statementBalance = ?, paidAmount = ?, paidDate = ?, isPaid = ?, paymentMethod = ?, notes = ?, extraPrincipal = ? WHERE id = ?`, [
                bill.accountId, bill.dueDate, bill.minPayment, bill.statementBalance, bill.paidAmount ?? null, bill.paidDate ?? null, bill.isPaid ? 1 : 0, bill.paymentMethod, bill.notes ?? null, bill.extraPrincipal ?? null, bill.id
            ]);
            dbInstance.run(`UPDATE accounts SET balance = ? WHERE id = ?`, [
                newBalance, bill.accountId
            ]);
        });
    },

    async deleteBill(billId: string): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for deleteBill.");
        db.run("DELETE FROM bills WHERE id = ?", [billId]);
        if (!this._inTransaction) saveDB();
    },

    // Connections Methods
    async getConnections(): Promise<Connection[]> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for getConnections.");
        const res = db.exec("SELECT * FROM connections");
        if (res.length === 0 || !res[0]?.values) return [];
        return res[0].values.map(row => rowToConnection(row as any[]));
    },
    
    async addFullConnection(connection: Connection): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for addFullConnection.");
        db.run(`INSERT INTO connections (id, name, role, company, lastContactDate, contactFrequency, createdAt) VALUES (?, ?, ?, ?, ?, ?, ?)`, [
            connection.id, connection.name, connection.role ?? null, connection.company ?? null, connection.lastContactDate, connection.contactFrequency, connection.createdAt
        ]);
        if (!this._inTransaction) saveDB();
    },

    async addConnection(connection: Omit<Connection, 'id' | 'createdAt' | 'lastContactDate'>): Promise<Connection> {
        const now = new Date().toISOString();
        const newConnection: Connection = { ...connection, id: crypto.randomUUID(), createdAt: now, lastContactDate: now };
        await this.addFullConnection(newConnection);
        return newConnection;
    },

    async updateConnection(connection: Connection): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for updateConnection.");
        db.run(`UPDATE connections SET name = ?, role = ?, company = ?, lastContactDate = ?, contactFrequency = ? WHERE id = ?`, [
            connection.name, connection.role ?? null, connection.company ?? null, connection.lastContactDate, connection.contactFrequency, connection.id
        ]);
        if (!this._inTransaction) saveDB();
    },

    async deleteConnection(connectionId: string): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for deleteConnection.");
        db.run("DELETE FROM connections WHERE id = ?", [connectionId]);
        if (!this._inTransaction) saveDB();
    },

    async seedInitialConnections(connectionsToSeed: Omit<Connection, 'id' | 'createdAt'>[]): Promise<void> {
        await this.initialize();
        const settings = await this.getSettings();
        if (settings.connectionsSeeded) return;
        
        const currentConnections = await this.getConnections();
        if (currentConnections.length > 0) {
            await this.updateSetting('connectionsSeeded', true);
            return;
        }

        await this.runInTransaction(async () => {
            for (const conn of connectionsToSeed) {
                 const newConnection: Connection = { ...conn, id: crypto.randomUUID(), createdAt: new Date().toISOString() };
                 await this.addFullConnection(newConnection);
            }
            await this.updateSetting('connectionsSeeded', true);
        });
    },
};