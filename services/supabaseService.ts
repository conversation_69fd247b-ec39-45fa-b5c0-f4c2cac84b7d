import { supabase } from './supabaseClient';
import type { Task, Settings, Priority, RecurrenceType, Account, AccountType, Bill, PaymentMethod, Connection, ContactFrequency } from '../types';

function assertClient() {
  if (!supabase) throw new Error('Supabase is not configured. Set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY.');
}

async function getUserId(): Promise<string> {
  assertClient();
  const { data, error } = await supabase!.auth.getUser();
  if (error || !data.user) throw new Error('Not authenticated. Please sign in.');
  return data.user.id;
}

// Mappers: DB row <-> App types
function rowToTask(row: any): Task {
  return {
    id: row.id,
    title: row.title,
    description: row.description ?? undefined,
    dueDate: row.due_date,
    dueTime: row.due_time,
    isCompleted: Boolean(row.is_completed),
    completedAt: row.completed_at ?? undefined,
    priority: row.priority as Priority,
    category: row.category ?? undefined,
    isRecurring: Bo<PERSON>an(row.is_recurring),
    recurrenceType: row.recurrence_type as RecurrenceType | undefined,
    recurrenceInterval: row.recurrence_interval ?? undefined,
    createdAt: row.created_at,
  };
}

function taskToRow(task: Task | (Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'> & { id?: string; createdAt?: string; isCompleted?: boolean; completedAt?: string | null })) {
  return {
    id: (task as any).id,
    title: task.title,
    description: task.description ?? null,
    due_date: task.dueDate,
    due_time: task.dueTime,
    is_completed: (task as any).isCompleted ?? false,
    completed_at: (task as any).completedAt ?? null,
    priority: task.priority,
    category: task.category ?? null,
    is_recurring: task.isRecurring,
    recurrence_type: task.recurrenceType ?? null,
    recurrence_interval: task.recurrenceInterval ?? null,
    created_at: (task as any).createdAt ?? null,
  };
}

function rowToAccount(row: any): Account {
  return {
    id: row.id,
    name: row.name,
    type: row.type as AccountType,
    balance: Number(row.balance),
    creditLimit: row.credit_limit ?? undefined,
    apr: row.apr ?? undefined,
    institution: row.institution ?? undefined,
    lastFourDigits: row.last_four_digits ?? undefined,
    paymentDay: row.payment_day ?? undefined,
    defaultPaymentMethod: row.default_payment_method as PaymentMethod | undefined,
    createdAt: row.created_at,
  };
}

function accountToRow(account: Account | Omit<Account, 'id' | 'createdAt'> & { id?: string; createdAt?: string }) {
  return {
    id: (account as any).id,
    name: account.name,
    type: account.type,
    balance: account.balance,
    credit_limit: account.creditLimit ?? null,
    apr: account.apr ?? null,
    institution: account.institution ?? null,
    last_four_digits: account.lastFourDigits ?? null,
    payment_day: account.paymentDay ?? null,
    default_payment_method: account.defaultPaymentMethod ?? null,
    created_at: (account as any).createdAt ?? null,
  };
}

function rowToBill(row: any): Bill {
  return {
    id: row.id,
    accountId: row.account_id,
    dueDate: row.due_date,
    minPayment: Number(row.min_payment),
    statementBalance: Number(row.statement_balance),
    paidAmount: row.paid_amount ?? undefined,
    paidDate: row.paid_date ?? undefined,
    isPaid: Boolean(row.is_paid),
    paymentMethod: row.payment_method as PaymentMethod,
    createdAt: row.created_at,
    notes: row.notes ?? undefined,
    extraPrincipal: row.extra_principal ?? undefined,
  };
}

function billToRow(bill: Bill | Omit<Bill, 'id' | 'createdAt'> & { id?: string; createdAt?: string }) {
  return {
    id: (bill as any).id,
    account_id: bill.accountId,
    due_date: bill.dueDate,
    min_payment: bill.minPayment,
    statement_balance: bill.statementBalance,
    paid_amount: (bill as any).paidAmount ?? null,
    paid_date: (bill as any).paidDate ?? null,
    is_paid: (bill as any).isPaid ?? false,
    payment_method: bill.paymentMethod,
    created_at: (bill as any).createdAt ?? null,
    notes: (bill as any).notes ?? null,
    extra_principal: (bill as any).extraPrincipal ?? null,
  };
}

function rowToConnection(row: any): Connection {
  return {
    id: row.id,
    name: row.name,
    role: row.role ?? undefined,
    company: row.company ?? undefined,
    lastContactDate: row.last_contact_date,
    contactFrequency: row.contact_frequency as ContactFrequency,
    createdAt: row.created_at,
  };
}

function connectionToRow(conn: Connection | (Omit<Connection, 'id' | 'createdAt' | 'lastContactDate'> & { id?: string; createdAt?: string; lastContactDate?: string })) {
  return {
    id: (conn as any).id,
    name: conn.name,
    role: conn.role ?? null,
    company: conn.company ?? null,
    last_contact_date: (conn as any).lastContactDate ?? new Date().toISOString(),
    contact_frequency: conn.contactFrequency,
    created_at: (conn as any).createdAt ?? null,
  };
}

export const supabaseService = {
  async getSettings(): Promise<Settings> {
    assertClient();
    const userId = await getUserId();
    const { data, error } = await supabase!
      .from('settings')
      .select('key,value')
      .eq('user_id', userId);
    if (error) throw new Error(error.message);
    const result: Settings = { showCompleted: true, seeded: false };
    for (const row of data ?? []) {
      if (row.key === 'showCompleted') result.showCompleted = row.value === 'true';
    }
    return result;
  },

  async updateSetting(key: keyof Settings, value: string | boolean): Promise<void> {
    assertClient();
    const userId = await getUserId();
    const payload = { user_id: userId, key, value: String(value) } as any;
    const { error } = await supabase!.from('settings').upsert(payload, { onConflict: 'user_id,key' });
    if (error) throw new Error(error.message);
  },

  // Tasks
  async getTasks(): Promise<Task[]> {
    assertClient();
    const { data, error } = await supabase!.from('tasks').select('*').order('created_at', { ascending: true });
    if (error) throw new Error(error.message);
    return (data ?? []).map(rowToTask);
  },

  async addFullTask(task: Task): Promise<void> {
    assertClient();
    const userId = await getUserId();
    const row = { ...taskToRow(task), user_id: userId };
    const { error } = await supabase!.from('tasks').insert(row);
    if (error) throw new Error(error.message);
  },

  async addTask(task: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>): Promise<Task> {
    assertClient();
    const userId = await getUserId();
    const now = new Date().toISOString();
    const row = { ...taskToRow({ ...task, createdAt: now } as any), user_id: userId };
    const { data, error } = await supabase!.from('tasks').insert(row).select('*').single();
    if (error) throw new Error(error.message);
    return rowToTask(data);
  },

  async updateTask(task: Task): Promise<void> {
    assertClient();
    const row = taskToRow(task);
    const { error } = await supabase!.from('tasks').update(row).eq('id', task.id);
    if (error) throw new Error(error.message);
  },

  async deleteTask(taskId: string): Promise<void> {
    assertClient();
    const { error } = await supabase!.from('tasks').delete().eq('id', taskId);
    if (error) throw new Error(error.message);
  },

  async runInTransaction(callback: () => Promise<void>): Promise<void> {
    // Supabase PostgREST does not support client-side transactions; execute sequentially
    await callback();
  },

  // Accounts
  async getAccounts(): Promise<Account[]> {
    assertClient();
    const { data, error } = await supabase!.from('accounts').select('*').order('created_at', { ascending: true });
    if (error) throw new Error(error.message);
    return (data ?? []).map(rowToAccount);
  },

  async addAccount(account: Omit<Account, 'id' | 'createdAt'>): Promise<Account> {
    assertClient();
    const userId = await getUserId();
    const row = { ...accountToRow(account as any), user_id: userId };
    const { data, error } = await supabase!.from('accounts').insert(row).select('*').single();
    if (error) throw new Error(error.message);
    return rowToAccount(data);
  },

  async updateAccount(account: Account): Promise<void> {
    assertClient();
    const row = accountToRow(account);
    const { error } = await supabase!.from('accounts').update(row).eq('id', account.id);
    if (error) throw new Error(error.message);
  },

  async deleteAccount(accountId: string): Promise<void> {
    assertClient();
    const { error } = await supabase!.from('accounts').delete().eq('id', accountId);
    if (error) throw new Error(error.message);
  },

  // Bills
  async getBills(): Promise<Bill[]> {
    assertClient();
    const { data, error } = await supabase!.from('bills').select('*').order('due_date', { ascending: true });
    if (error) throw new Error(error.message);
    return (data ?? []).map(rowToBill);
  },

  async addFullBill(bill: Bill): Promise<void> {
    assertClient();
    const userId = await getUserId();
    const row = { ...billToRow(bill), user_id: userId };
    const { error } = await supabase!.from('bills').insert(row);
    if (error) throw new Error(error.message);
  },

  async addBill(bill: Omit<Bill, 'id' | 'createdAt' | 'isPaid'>): Promise<Bill> {
    assertClient();
    const userId = await getUserId();
    const now = new Date().toISOString();
    const row = { ...billToRow({ ...bill, createdAt: now } as any), user_id: userId };
    const { data, error } = await supabase!.from('bills').insert(row).select('*').single();
    if (error) throw new Error(error.message);
    return rowToBill(data);
  },

  async updateBill(bill: Bill): Promise<void> {
    assertClient();
    const row = billToRow(bill);
    const { error } = await supabase!.from('bills').update(row).eq('id', bill.id);
    if (error) throw new Error(error.message);
  },

  async updateBillAndAccount(bill: Bill, newBalance: number): Promise<void> {
    assertClient();
    // No client-side transaction; perform sequential updates
    await this.updateBill(bill);
    const { error } = await supabase!.from('accounts').update({ balance: newBalance }).eq('id', bill.accountId);
    if (error) throw new Error(error.message);
  },

  async deleteBill(billId: string): Promise<void> {
    assertClient();
    const { error } = await supabase!.from('bills').delete().eq('id', billId);
    if (error) throw new Error(error.message);
  },

  // Connections
  async getConnections(): Promise<Connection[]> {
    assertClient();
    const { data, error } = await supabase!.from('connections').select('*').order('created_at', { ascending: true });
    if (error) throw new Error(error.message);
    return (data ?? []).map(rowToConnection);
  },

  async addFullConnection(connection: Connection): Promise<void> {
    assertClient();
    const userId = await getUserId();
    const row = { ...connectionToRow(connection), user_id: userId };
    const { error } = await supabase!.from('connections').insert(row);
    if (error) throw new Error(error.message);
  },

  async addConnection(connection: Omit<Connection, 'id' | 'createdAt' | 'lastContactDate'>): Promise<Connection> {
    assertClient();
    const userId = await getUserId();
    const now = new Date().toISOString();
    const row = { ...connectionToRow({ ...connection, createdAt: now, lastContactDate: now } as any), user_id: userId };
    const { data, error } = await supabase!.from('connections').insert(row).select('*').single();
    if (error) throw new Error(error.message);
    return rowToConnection(data);
  },

  async updateConnection(connection: Connection): Promise<void> {
    assertClient();
    const row = connectionToRow(connection);
    const { error } = await supabase!.from('connections').update(row).eq('id', connection.id);
    if (error) throw new Error(error.message);
  },

  async deleteConnection(connectionId: string): Promise<void> {
    assertClient();
    const { error } = await supabase!.from('connections').delete().eq('id', connectionId);
    if (error) throw new Error(error.message);
  },
};
