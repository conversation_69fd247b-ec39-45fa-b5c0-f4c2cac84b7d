
import React, { useContext } from 'react';
import { ConnectionsContext } from '../contexts/ConnectionsContext';
import SummaryCards from '../components/connections/SummaryCards';
import ConnectionList from '../components/connections/ConnectionList';

const ConnectionsView: React.FC = () => {
    const connectionsContext = useContext(ConnectionsContext);

    if (!connectionsContext) {
        throw new Error("ConnectionsView must be used within a ConnectionsProvider");
    }

    const { connections, isLoading } = connectionsContext;

    if (isLoading) {
         return (
            <div className="flex items-center justify-center h-full">
                <div className="flex flex-col items-center">
                    <div className="w-12 h-12 border-4 border-brand-secondary border-t-transparent rounded-full animate-spin"></div>
                    <p className="mt-4 text-brand-secondary">Loading your connections...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <SummaryCards connections={connections} />
            <ConnectionList connections={connections} />
        </div>
    );
};

export default ConnectionsView;