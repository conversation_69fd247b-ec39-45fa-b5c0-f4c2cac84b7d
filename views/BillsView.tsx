
import React, { useContext } from 'react';
import { BillsContext } from '../contexts/BillsContext';
import { AccountType } from '../types';
import SummaryWidget from '../components/bills/SummaryWidget';
import AccountGroup from '../components/bills/AccountGroup';
import UpcomingBills from '../components/bills/UpcomingBills';

const BillsView: React.FC = () => {
    const billsContext = useContext(BillsContext);

    if (!billsContext) {
        throw new Error("BillsView must be used within a BillsProvider");
    }

    const { accounts, bills, isLoading } = billsContext;
    
    if (isLoading) {
         return (
            <div className="flex items-center justify-center h-full">
                <div className="flex flex-col items-center">
                    <div className="w-12 h-12 border-4 border-brand-secondary border-t-transparent rounded-full animate-spin"></div>
                    <p className="mt-4 text-brand-secondary">Loading financial data...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <SummaryWidget accounts={accounts} />
            <UpcomingBills bills={bills} accounts={accounts} />
            <div className="space-y-4">
                <AccountGroup 
                    title="Credit Cards"
                    type={AccountType.CreditCard}
                    accounts={accounts.filter(a => a.type === AccountType.CreditCard)}
                    allAccounts={accounts}
                    bills={bills}
                />
                <AccountGroup 
                    title="Loans"
                    type={AccountType.Loan}
                    accounts={accounts.filter(a => a.type === AccountType.Loan)}
                    allAccounts={accounts}
                    bills={bills}
                />
            </div>
        </div>
    );
};

export default BillsView;
