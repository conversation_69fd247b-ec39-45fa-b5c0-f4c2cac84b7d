
import React, { createContext, useState, useCallback, useEffect, ReactNode } from 'react';
import { supabase } from '../services/supabaseClient';

interface AuthContextType {
    isAuthenticated: boolean;
    isInitializing: boolean;
    login: (username: string, password: string) => Promise<void>;
    logout: () => void;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
    const [isInitializing, setIsInitializing] = useState<boolean>(true);

    useEffect(() => {
        const init = async () => {
            const { data } = await supabase!.auth.getSession();
            setIsAuthenticated(!!data.session);
            setIsInitializing(false);
        };
        init();
        const { data: sub } = supabase!.auth.onAuthStateChange((_event, session) => {
            setIsAuthenticated(!!session);
        });
        return () => {
            sub.subscription.unsubscribe();
        };
    }, []);
    
    const login = useCallback(async (email: string, password: string): Promise<void> => {
        const { error } = await supabase!.auth.signInWithPassword({ email, password });
        if (error) throw new Error(error.message);
        setIsAuthenticated(true);
    }, []);

    const logout = useCallback(async () => {
        await supabase!.auth.signOut();
        setIsAuthenticated(false);
    }, []);

    const value = { isAuthenticated, isInitializing, login, logout };

    return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
