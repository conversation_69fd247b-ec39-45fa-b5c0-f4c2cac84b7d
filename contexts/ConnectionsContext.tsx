
import React, { createContext, ReactNode, useState, useEffect, useCallback } from 'react';
import { Connection, ConnectionsContextType, ContactFrequency } from '../types';
import { supabaseService } from '../services/supabaseService';

export const ConnectionsContext = createContext<ConnectionsContextType | undefined>(undefined);

const getPastDateISO = (daysAgo: number): string => {
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    return date.toISOString();
};

const initialSeedConnections: Omit<Connection, 'id' | 'createdAt'>[] = [
    { name: '<PERSON>', role: 'Project Manager', company: 'Innovate Corp', lastContactDate: getPastDateISO(5), contactFrequency: ContactFrequency.Weekly },
    { name: '<PERSON>', role: 'Senior Developer', company: 'Tech Solutions', lastContactDate: getPastDateISO(25), contactFrequency: ContactFrequency.Monthly },
    { name: '<PERSON>', role: 'UX Designer', company: 'Creative Minds', lastContactDate: getPastDateISO(80), contactFrequency: ContactFrequency.Quarterly },
    { name: '<PERSON> <PERSON>', role: 'Marketing Lead', company: 'Growth Co.', lastContactDate: getPastDateISO(40), contactFrequency: ContactFrequency.Monthly },
    { name: 'Ethan Davis', role: 'Former Colleague', company: 'Old Company Inc.', lastContactDate: getPastDateISO(200), contactFrequency: ContactFrequency.Annually },
    { name: 'Fiona Garcia', role: 'Recruiter', company: 'Talent Finders', lastContactDate: getPastDateISO(100), contactFrequency: ContactFrequency.Quarterly },
];


export const ConnectionsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [connections, setConnections] = useState<Connection[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    const loadData = useCallback(async () => {
        setIsLoading(true);
        const dbConnections = await supabaseService.getConnections();
        setConnections(dbConnections);
        setIsLoading(false);
    }, []);

    // No seeding on Supabase; rely on user's data
    
    useEffect(() => {
        const init = async () => {
            await loadData();
        }
        init();
    }, [loadData]);

    const refreshData = async () => {
        await loadData();
    };

    const addConnection = async (connection: Omit<Connection, 'id' | 'createdAt' | 'lastContactDate'>) => {
        await supabaseService.addConnection(connection);
        await refreshData();
    };

    const updateConnection = async (updatedConnection: Connection) => {
        await supabaseService.updateConnection(updatedConnection);
        await refreshData();
    };

    const deleteConnection = async (connectionId: string) => {
        await supabaseService.deleteConnection(connectionId);
        await refreshData();
    };

    const value = { connections, addConnection, updateConnection, deleteConnection, isLoading };

    return (
        <ConnectionsContext.Provider value={value}>
            {children}
        </ConnectionsContext.Provider>
    );
};
