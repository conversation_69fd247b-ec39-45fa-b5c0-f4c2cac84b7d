import React, { createContext, ReactNode, useEffect, useState, useCallback } from 'react';
import { Settings } from '../types';
import { supabaseService } from '../services/supabaseService';

interface SettingsContextType {
    showCompleted: boolean;
    setShowCompleted: (show: boolean) => void;
}

export const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const SettingsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [settings, setSettings] = useState<Settings>({ showCompleted: true, seeded: false });

    const loadSettings = useCallback(async () => {
        const s = await supabaseService.getSettings();
        setSettings(s);
    }, []);

    useEffect(() => {
        loadSettings();
    }, [loadSettings]);

    const setShowCompleted = useCallback(async (show: boolean) => {
        setSettings(s => ({ ...s, showCompleted: show }));
        await supabaseService.updateSetting('showCompleted', show);
    }, []);

    const value = {
        showCompleted: settings.showCompleted,
        setShowCompleted
    };

    return (
        <SettingsContext.Provider value={value}>
            {children}
        </SettingsContext.Provider>
    );
};
