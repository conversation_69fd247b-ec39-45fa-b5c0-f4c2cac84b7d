import React, { createContext, ReactNode, useState, useEffect, useCallback } from 'react';
import { Task, Priority, RecurrenceType, TaskContextType } from '../types';
import { getNextRecurrenceDate } from '../utils/dateUtils';
import { supabaseService } from '../services/supabaseService';

export const TaskContext = createContext<TaskContextType | undefined>(undefined);

// No seeding when using Supabase; start with user's data only


export const TaskProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [tasks, setTasks] = useState<Task[]>([]);
    const [isSeeding, setIsSeeding] = useState(true);

    const loadTasks = useCallback(async () => {
        setIsSeeding(true);
        const dbTasks = await supabaseService.getTasks();
        setTasks(dbTasks);
        setIsSeeding(false);
    }, []);

    useEffect(() => {
        loadTasks();
    }, [loadTasks]);

    const refreshTasks = async () => {
        const updatedTasks = await supabaseService.getTasks();
        setTasks(updatedTasks);
    };

    const addTask = async (task: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>) => {
        await supabaseService.addTask(task);
        await refreshTasks();
    };
    
    const updateTask = async (updatedTask: Task) => {
        if (updatedTask.isRecurring && updatedTask.isCompleted) {
            const originalTask = tasks.find(t => t.id === updatedTask.id);
            if(originalTask) {
                // Use a transaction to ensure both operations succeed or fail together
                await supabaseService.runInTransaction(async () => {
                    // 1. Update the original recurring task for its next occurrence
                    const nextDueDate = getNextRecurrenceDate(originalTask);
                    await supabaseService.updateTask({ ...originalTask, dueDate: nextDueDate });
                    
                    // 2. Create a new, non-recurring, completed task instance
                    const completedInstance: Task = {
                        ...originalTask,
                        id: crypto.randomUUID(),
                        isRecurring: false,
                        isCompleted: true,
                        completedAt: new Date().toISOString(),
                        createdAt: new Date().toISOString(), 
                    };
                    await supabaseService.addFullTask(completedInstance);
                });
            }
        } else {
            // Standard update for non-recurring tasks or other field changes
            const taskToSave = { ...updatedTask, completedAt: updatedTask.isCompleted ? new Date().toISOString() : undefined };
            await supabaseService.updateTask(taskToSave);
        }
        await refreshTasks();
    };

    const deleteTask = async (taskId: string) => {
        await supabaseService.deleteTask(taskId);
        await refreshTasks();
    };
    
    const value = { tasks, addTask, updateTask, deleteTask, isSeeding };

    return (
        <TaskContext.Provider value={value}>
            {children}
        </TaskContext.Provider>
    );
};
