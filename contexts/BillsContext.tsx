

import React, { createContext, ReactNode, useState, useEffect, useCallback } from 'react';
import { Account, Bill, BillsContextType, AccountType, PaymentMethod } from '../types';
import { supabaseService } from '../services/supabaseService';

export const BillsContext = createContext<BillsContextType | undefined>(undefined);

const getFutureDate = (days: number): string => {
    const date = new Date();
    date.setDate(date.getDate() + days);
    return date.toISOString().split('T')[0];
};

const getPastDateISO = (daysAgo: number): string => {
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    return date.toISOString();
};

const getPastDateYMD = (daysAgo: number): string => {
    return getPastDateISO(daysAgo).split('T')[0];
};

const initialSeedAccounts: Omit<Account, 'id' | 'createdAt'>[] = [
    { name: 'Checking Account', type: AccountType.Cash, balance: 5476.72, institution: 'Main Bank' },
    { name: 'Unlimited', lastFourDigits: '5944', type: AccountType.CreditCard, balance: 93.77, creditLimit: 5000, institution: 'Chase', apr: 21.99, paymentDay: 15, defaultPaymentMethod: 'Auto Pay' },
    { name: 'Blue Sky', lastFourDigits: '1007', type: AccountType.CreditCard, balance: 1568.54, creditLimit: 10000, institution: 'American Express', apr: 18.24, paymentDay: 20, defaultPaymentMethod: 'Manual' },
    { name: 'Discover it Card', lastFourDigits: '2689', type: AccountType.CreditCard, balance: 3420.00, creditLimit: 7500, institution: 'Discover', apr: 24.99, paymentDay: 28 },
    { name: 'Citi Diamond Preferred', lastFourDigits: '2877', type: AccountType.CreditCard, balance: 7058.63, creditLimit: 8000, institution: 'Citi', apr: 22.49, paymentDay: 5 },
    { name: 'Personal Loan', lastFourDigits: '1003', type: AccountType.Loan, balance: 9762.09, institution: 'American Express', apr: 7.98, paymentDay: 10, defaultPaymentMethod: 'Auto Pay' },
    { name: '2020 Toyota Highlander', lastFourDigits: '8548', type: AccountType.Loan, balance: 24145.25, institution: 'Toyota Financial', apr: 3.49, paymentDay: 1, defaultPaymentMethod: 'Auto Pay' },
    { name: 'Student Loan', type: AccountType.Loan, balance: 12373.20, institution: 'Aidvantage', apr: 5.05, paymentDay: 22, defaultPaymentMethod: 'Manual' },
];

const initialSeedBills: (Omit<Bill, 'id' | 'createdAt' | 'isPaid' | 'accountId'> & { accountName: string })[] = [
    { accountName: 'Unlimited', dueDate: getFutureDate(3), statementBalance: 93.77, minPayment: 25, paymentMethod: 'Auto Pay' },
    { accountName: 'Blue Sky', dueDate: getFutureDate(8), statementBalance: 1568.54, minPayment: 40, paymentMethod: 'Manual' },
    { accountName: '2020 Toyota Highlander', dueDate: getFutureDate(12), statementBalance: 450.78, minPayment: 450.78, paymentMethod: 'Auto Pay' },
    { accountName: 'Citi Diamond Preferred', dueDate: getFutureDate(-2), statementBalance: 350.00, minPayment: 50, paymentMethod: 'Manual'},
];

const initialSeedPaidBills: (Omit<Bill, 'id' | 'createdAt' | 'accountId'> & { accountName: string })[] = [
    { accountName: 'Unlimited', dueDate: getPastDateYMD(27), statementBalance: 85.43, minPayment: 25, paymentMethod: 'Auto Pay', isPaid: true, paidAmount: 85.43, paidDate: getPastDateISO(28) },
    { accountName: 'Blue Sky', dueDate: getPastDateYMD(22), statementBalance: 1602.11, minPayment: 40, paymentMethod: 'Manual', isPaid: true, paidAmount: 1602.11, paidDate: getPastDateISO(23) },
    { accountName: 'Discover it Card', dueDate: getPastDateYMD(2), statementBalance: 3395.80, minPayment: 100, paymentMethod: 'Manual', isPaid: true, paidAmount: 3395.80, paidDate: getPastDateISO(3) },
    { accountName: 'Citi Diamond Preferred', dueDate: getPastDateYMD(25), statementBalance: 350.00, minPayment: 50, paymentMethod: 'Manual', isPaid: true, paidAmount: 100.00, paidDate: getPastDateISO(26) },
    { accountName: 'Personal Loan', dueDate: getPastDateYMD(20), statementBalance: 205.88, minPayment: 205.88, paymentMethod: 'Auto Pay', isPaid: true, paidAmount: 205.88, paidDate: getPastDateISO(21) },
    { accountName: '2020 Toyota Highlander', dueDate: getPastDateYMD(29), statementBalance: 450.78, minPayment: 450.78, paymentMethod: 'Auto Pay', isPaid: true, paidAmount: 450.78, paidDate: getPastDateISO(30) },
    { accountName: 'Student Loan', dueDate: getPastDateYMD(8), statementBalance: 150.00, minPayment: 150.00, paymentMethod: 'Manual', isPaid: true, paidAmount: 150.00, paidDate: getPastDateISO(9) },
];

export const BillsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [accounts, setAccounts] = useState<Account[]>([]);
    const [bills, setBills] = useState<Bill[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    const loadData = useCallback(async () => {
        setIsLoading(true);
        const [dbAccounts, dbBills] = await Promise.all([supabaseService.getAccounts(), supabaseService.getBills()]);
        setAccounts(dbAccounts);
        setBills(dbBills);
        setIsLoading(false);
    }, []);

    // No seeding on Supabase path; rely on user's data

    useEffect(() => {
        const init = async () => {
            await loadData();
        };
        init();
    }, [loadData]);
    
    const refreshData = async () => {
        await loadData();
    };
    
    const addAccount = async (account: Omit<Account, 'id' | 'createdAt'>) => {
        await supabaseService.addAccount(account);
        await refreshData();
    };

    const updateAccount = async (updatedAccount: Account) => {
        await supabaseService.updateAccount(updatedAccount);
        await refreshData();
    };

    const deleteAccount = async (accountId: string) => {
        await supabaseService.deleteAccount(accountId);
        await refreshData();
    };
    
    const addBill = async (bill: Omit<Bill, 'id' | 'createdAt' | 'isPaid'>) => {
        await supabaseService.addBill(bill);
        await refreshData();
    };

    const updateBill = async (updatedBill: Bill, newBalance?: number) => {
        if (typeof newBalance === 'number') {
            await supabaseService.updateBillAndAccount(updatedBill, newBalance);
        } else {
            await supabaseService.updateBill(updatedBill);
        }
        await refreshData();
    };

    const deleteBill = async (billId: string) => {
        await supabaseService.deleteBill(billId);
        await refreshData();
    };

    const value = { accounts, bills, addAccount, updateAccount, deleteAccount, addBill, updateBill, deleteBill, isLoading };

    return (
        <BillsContext.Provider value={value}>
            {children}
        </BillsContext.Provider>
    );
};
