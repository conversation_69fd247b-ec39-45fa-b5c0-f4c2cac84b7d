# LifeTracker: Simple, Ultra‑Robust, Vercel‑Ready

Goals: strictly single user, no growth plans, minimum moving parts, robust in production on Vercel. No geolocation/camera/mic. Add auto log‑off on inactivity.

## Principles
- Keep it static: build once, serve files. No server code unless essential.
- Eliminate external runtime dependencies (CDNs) to reduce failure points.
- Prefer predictable, self‑hosted assets for CSS and WASM.
- Use tight security headers with a strict CSP compatible with the static bundle.
- Accept client‑side persistence (single user), but add export/import for backups.
- Implement auto log‑off based on inactivity and across tabs.

## High‑Value Changes (Minimal Complexity)
1) Remove runtime CDNs, bundle everything
- Tailwind: switch to compiled Tailwind (PostCSS) and purge unused classes.
- sql.js WASM: self‑host `sql-wasm.wasm` and reference it with `locateFile`.
- Remove `importmap` in `index.html`; rely on Vite bundling.

2) Tight security headers (vercel.json)
- Strict CSP with `default-src 'self'` and no inline scripts/styles.
- Disable unused features via `Permissions-Policy`.
- HSTS, X-Content-Type-Options, Referrer-Policy, and frame busting.

3) Auto log‑off on inactivity (client‑side)
- Watch keyboard/mouse/touch/visibility events; log out after idle timeout.
- Sync logout across tabs with `BroadcastChannel`.

4) Reliability improvements
- Better error handling for DB init and a user‑visible retry.
- Optional: simple data backup (Export/Import DB) for restores.

---

## Step‑by‑Step

### A) Self‑Host sql.js WASM
1. Move WASM into `public/` (Vite serves it at `/sql-wasm.wasm`).
2. Update `services/dbService.ts` init:

```ts
// Before: fetching from jsDelivr
// const wasmResponse = await fetch('https://cdn.jsdelivr.net/npm/sql.js@1.13.0/dist/sql-wasm.wasm');
// const wasmBinary = await wasmResponse.arrayBuffer();
// SQL = await initSqlJs({ wasmBinary });

// After: Vite serves from /public
SQL = await initSqlJs({
  locateFile: (file) => `/sql-wasm.wasm`
});
```

Benefit: no external network requirement for DB engine; faster, cacheable, and simpler CSP.

### B) Compile Tailwind (remove CDN)
1. Install Tailwind, PostCSS, and autoprefixer, add configs:

```bash
npm i -D tailwindcss postcss autoprefixer @vitejs/plugin-react-swc
npx tailwindcss init -p
```

2. Configure `tailwind.config.js` content paths to include `*.tsx` and `index.html`.
3. Create `src/index.css` with:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

4. Import `src/index.css` in `index.tsx` and remove the Tailwind CDN `<script>` and inline style/config from `index.html`.
5. Add `@vitejs/plugin-react-swc` to `vite.config.ts` for better DX (fast refresh).

Benefit: tiny production CSS, no inline scripts, strict CSP possible.

### C) Remove `importmap` and external ESM
- Delete the `<script type="importmap">` block from `index.html`.
- Ensure `react`, `react-dom`, and `sql.js` are dependencies in `package.json` (already present) and let Vite bundle them.

### D) Security Headers (vercel.json)
Add a `vercel.json` with strict headers. If you keep the weather API, whitelist it in `connect-src`. If you disable weather, remove it entirely.

```json
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        { "key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload" },
        { "key": "X-Content-Type-Options", "value": "nosniff" },
        { "key": "Referrer-Policy", "value": "no-referrer" },
        { "key": "X-Frame-Options", "value": "DENY" },
        { "key": "Permissions-Policy", "value": "geolocation=(), microphone=(), camera=(), payment=(), usb=(), accelerometer=(), gyroscope=(), magnetometer=()" },
        { "key": "Content-Security-Policy",
          "value": "default-src 'self'; script-src 'self'; style-src 'self'; img-src 'self' data:; font-src 'self'; connect-src 'self' https://api.open-meteo.com; object-src 'none'; base-uri 'self'; frame-ancestors 'none'" }
      ]
    }
  ]
}
```

Notes:
- If you disable the weather widget, remove `https://api.open-meteo.com` from `connect-src`.
- Removing all inline scripts/styles makes this CSP workable.

### E) Auto Log‑Off on Inactivity
Add a tiny hook and wire it into the authenticated app shell.

```ts
// hooks/useIdleLogout.ts
import { useEffect, useRef } from 'react';

type Opts = { timeoutMs?: number; onLogout: () => void };

export function useIdleLogout({ timeoutMs = 10 * 60 * 1000, onLogout }: Opts) {
  const timer = useRef<number | null>(null);
  const bcRef = useRef<BroadcastChannel | null>(null);

  useEffect(() => {
    bcRef.current = new BroadcastChannel('lifetracker-auth');
    bcRef.current.onmessage = (e) => {
      if (e.data === 'logout') onLogout();
    };
    return () => bcRef.current?.close();
  }, [onLogout]);

  useEffect(() => {
    const reset = () => {
      if (timer.current) window.clearTimeout(timer.current);
      timer.current = window.setTimeout(() => {
        bcRef.current?.postMessage('logout');
        onLogout();
      }, timeoutMs) as unknown as number;
    };

    const events: (keyof DocumentEventMap)[] = [
      'mousemove', 'mousedown', 'keydown', 'scroll', 'touchstart', 'visibilitychange'
    ];
    const handler = () => {
      if (document.visibilityState === 'visible') reset();
    };
    events.forEach(ev => window.addEventListener(ev, handler, { passive: true }));
    reset();
    return () => {
      if (timer.current) window.clearTimeout(timer.current);
      events.forEach(ev => window.removeEventListener(ev, handler as any));
    };
  }, [onLogout, timeoutMs]);
}
```

Usage (after successful auth):

```tsx
// In a component that renders only when authenticated (e.g., MainLayout wrapper)
import { useContext } from 'react';
import { AuthContext } from '@/contexts/AuthContext';
import { useIdleLogout } from '@/hooks/useIdleLogout';

function AuthenticatedShell({ children }: { children: React.ReactNode }) {
  const auth = useContext(AuthContext)!;
  useIdleLogout({ timeoutMs: 10 * 60 * 1000, onLogout: auth.logout });
  return <>{children}</>;
}
```

Optional: show a 60‑second countdown modal before auto‑logout for a better UX.

### F) DB Init Resilience
- Don’t set `isInitialized = true` unless `db` is non‑null.
- Show a user‑visible error if initialization fails and provide a Retry button that re‑calls `dbService.initialize()`.

### G) Data Backup (Export/Import)
Add two simple actions in Settings:

```ts
// Export
const data = db.export();
const blob = new Blob([data], { type: 'application/octet-stream' });
// trigger download (e.g., createObjectURL)

// Import
// read ArrayBuffer from file input
db = new SQL.Database(new Uint8Array(buffer));
saveDB();
```

This provides a simple manual backup/restore for a single user.

---

## Keep / Remove
- Keep: client‑side only app and storage for single user simplicity.
- Keep: weather widget only if you’re OK with the external call; otherwise disable it for a fully self‑contained app and stricter CSP.
- Remove: hardcoded credentials from source; change them now. Client‑side login is best treated as a convenience, not a security boundary.

## Vercel Settings
- Framework Preset: Vite.
- Build Command: `npm run build`.
- Output Directory: `dist`.
- Include `vercel.json` from above for headers.

## Notes on Limits
- localStorage has small quotas (~5–10MB). If you expect more data, consider switching to IndexedDB persistence for sql.js. For now, single‑user demo‑scale data is fine.
- Private browsing modes may reduce storage; handle init/save errors gracefully.

## Optional (if you later want more hardening)
- Add a service worker to cache assets for offline use (one more moving part).
- Migrate persistence to IndexedDB (via OPFS/absurd‑sql) to avoid full‑DB exports and size limits.
- Replace client login with a static‑site friendly gate (requires server/edge layer), which adds complexity and is out of scope for “strictly static + simple”.

---

## Summary
These changes remove runtime external dependencies, enable a tight CSP, add idle logout, improve DB robustness, and keep the deployment fully static and Vercel‑compliant. They preserve simplicity while materially improving reliability and security for a single‑user app.

