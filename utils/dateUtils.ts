
import { Task, RecurrenceType, ContactFrequency } from '../types';

export function formatRelativeDate(dueDate: string, dueTime: string): { text: string; isOverdue: boolean } {
    const now = new Date();
    const dueDateTime = new Date(`${dueDate}T${dueTime}`);

    const isTaskOverdue = now > dueDateTime;

    const timeString = dueDateTime.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });

    // For day-based comparisons (Today, Tomorrow, Overdue by X days)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const dueDay = new Date(dueDate);
    // Adjust for timezone offset to treat date as local when creating from YYYY-MM-DD string
    dueDay.setMinutes(dueDay.getMinutes() + dueDay.getTimezoneOffset());
    dueDay.setHours(0, 0, 0, 0);

    if (isTaskOverdue) {
        const overdueDiffTime = today.getTime() - dueDay.getTime();
        const overdueDiffDays = Math.ceil(overdueDiffTime / (1000 * 60 * 60 * 24));
        
        if (overdueDiffDays > 0) {
            return { text: `Overdue by ${overdueDiffDays} day${overdueDiffDays > 1 ? 's' : ''}`, isOverdue: true };
        } else {
            // Was due earlier today
            return { text: `Overdue since ${timeString}`, isOverdue: true };
        }
    }

    const diffTime = dueDay.getTime() - today.getTime();
    const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
        return { text: `Due Today at ${timeString}`, isOverdue: false };
    }
    if (diffDays === 1) {
        return { text: `Due Tomorrow at ${timeString}`, isOverdue: false };
    }

    // For dates further in the future, show the actual date.
    const dateString = dueDateTime.toLocaleDateString(undefined, {
        month: 'short',
        day: 'numeric',
        year: dueDateTime.getFullYear() !== today.getFullYear() ? 'numeric' : undefined,
    });

    return { text: `Due ${dateString} at ${timeString}`, isOverdue: false };
}

export function formatBillDueDate(dueDate: string): { text: string; isOverdue: boolean } {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const dueDay = new Date(dueDate);
    dueDay.setMinutes(dueDay.getMinutes() + dueDay.getTimezoneOffset());
    dueDay.setHours(0, 0, 0, 0);

    const diffTime = dueDay.getTime() - today.getTime();
    const diffDays = Math.round(diffTime / (1000 * 3600 * 24));

    if (diffDays < 0) {
        const overdueDays = Math.abs(diffDays);
        return { text: `Overdue by ${overdueDays} day${overdueDays > 1 ? 's' : ''}`, isOverdue: true };
    }
    if (diffDays === 0) {
        return { text: `Due Today`, isOverdue: false };
    }
    if (diffDays === 1) {
        return { text: `Due Tomorrow`, isOverdue: false };
    }
    return { text: `Due in ${diffDays} days`, isOverdue: false };
}


export function formatCompletedDate(completedAt: string): string {
    const date = new Date(completedAt);
    return `Completed ${date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}`;
}

export function getNextRecurrenceDate(task: Task): string {
    const { dueDate, recurrenceType, recurrenceInterval = 1 } = task;
    const currentDueDate = new Date(dueDate);
    currentDueDate.setMinutes(currentDueDate.getMinutes() + currentDueDate.getTimezoneOffset());
    
    switch (recurrenceType) {
        case RecurrenceType.Daily:
            currentDueDate.setDate(currentDueDate.getDate() + recurrenceInterval);
            break;
        case RecurrenceType.Weekly:
            currentDueDate.setDate(currentDueDate.getDate() + 7 * recurrenceInterval);
            break;
        case RecurrenceType.Monthly:
            currentDueDate.setMonth(currentDueDate.getMonth() + recurrenceInterval);
            break;
        case RecurrenceType.Yearly:
            currentDueDate.setFullYear(currentDueDate.getFullYear() + recurrenceInterval);
            break;
        default:
            // Should not happen, but return a sensible default
            currentDueDate.setDate(currentDueDate.getDate() + 1);
    }
    return currentDueDate.toISOString().split('T')[0];
}

// Connections Date Utils
export enum FollowUpStatus {
    Overdue = 'Overdue',
    DueSoon = 'DueSoon',
    Ok = 'Ok',
}

export function getNextContactDate(lastContact: string, frequency: ContactFrequency): Date {
    const date = new Date(lastContact);
    switch (frequency) {
        case ContactFrequency.Weekly:
            date.setDate(date.getDate() + 7);
            break;
        case ContactFrequency.Monthly:
            date.setMonth(date.getMonth() + 1);
            break;
        case ContactFrequency.Quarterly:
            date.setMonth(date.getMonth() + 3);
            break;
        case ContactFrequency.Annually:
            date.setFullYear(date.getFullYear() + 1);
            break;
    }
    return date;
}

export function getFollowUpStatus(lastContactDate: string, frequency: ContactFrequency): { status: FollowUpStatus; days: number } {
    const nextDueDate = getNextContactDate(lastContactDate, frequency);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    nextDueDate.setHours(0, 0, 0, 0);

    const diffTime = nextDueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
        return { status: FollowUpStatus.Overdue, days: Math.abs(diffDays) };
    }
    if (diffDays <= 7) {
        return { status: FollowUpStatus.DueSoon, days: diffDays };
    }
    return { status: FollowUpStatus.Ok, days: diffDays };
}

export function formatLastContact(lastContactDate: string): string {
    const contactDate = new Date(lastContactDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Create a new date object for comparison to avoid timezone issues with just date strings
    const contactDay = new Date(contactDate.getFullYear(), contactDate.getMonth(), contactDate.getDate());

    const diffTime = today.getTime() - contactDay.getTime();
    const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 30) return `${diffDays} days ago`;
    
    return `on ${contactDay.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`;
}