
export enum Priority {
    Low = 'Low',
    Medium = 'Medium',
    High = 'High'
}

export enum RecurrenceType {
    Daily = 'Daily',
    Weekly = 'Weekly',
    Monthly = 'Monthly',
    Yearly = 'Yearly'
}

export interface Task {
    id: string;
    title: string;
    description?: string;
    dueDate: string; // YYYY-MM-DD
    dueTime: string; // HH:mm
    isCompleted: boolean;
    completedAt?: string; // ISO string
    priority: Priority;
    category?: string;
    isRecurring: boolean;
    recurrenceType?: RecurrenceType;
    recurrenceInterval?: number;
    createdAt: string; // ISO string
}

export interface WeatherData {
    locationName: string;
    current: {
        temperature: number;
        weatherCode: number;
        feelsLike: number;
    };
    forecast: {
        date: string; // YYYY-MM-DD
        weatherCode: number;
        tempHigh: number;
        tempLow: number;
        precipitation?: number;
    }[];
}

export type View = 'tasks' | 'bills' | 'connections' | 'settings' | 'profile';

export interface Settings {
    showCompleted: boolean;
    seeded: boolean;
}

export interface TaskContextType {
    tasks: Task[];
    addTask: (task: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>) => void;
    updateTask: (updatedTask: Task) => void;
    deleteTask: (taskId: string) => void;
    isSeeding: boolean;
}

// Financial Types
export enum AccountType {
    CreditCard = 'Credit Card',
    Loan = 'Loan',
    Cash = 'Cash'
}

export interface Account {
    id: string;
    name: string;
    type: AccountType;
    balance: number;
    creditLimit?: number;
    apr?: number;
    institution?: string;
    lastFourDigits?: string;
    paymentDay?: number; // Day of the month
    defaultPaymentMethod?: PaymentMethod;
    createdAt: string; // ISO string
}

export type PaymentMethod = 'Auto Pay' | 'Manual';

export interface Bill {
    id: string;
    accountId: string;
    dueDate: string; // YYYY-MM-DD
    minPayment: number;
    statementBalance: number;
    paidAmount?: number;
    paidDate?: string; // ISO string
    isPaid: boolean;
    paymentMethod: PaymentMethod;
    notes?: string;
    extraPrincipal?: number;
    createdAt: string; // ISO string
}

export interface BillsContextType {
    accounts: Account[];
    bills: Bill[];
    addAccount: (account: Omit<Account, 'id' | 'createdAt'>) => Promise<void>;
    updateAccount: (updatedAccount: Account) => Promise<void>;
    deleteAccount: (accountId: string) => Promise<void>;
    addBill: (bill: Omit<Bill, 'id' | 'createdAt' | 'isPaid'>) => Promise<void>;
    updateBill: (updatedBill: Bill, newBalance?: number) => Promise<void>;
    deleteBill: (billId: string) => Promise<void>;
    isLoading: boolean;
}

// Connections Types
export enum ContactFrequency {
    Weekly = 'Weekly',
    Monthly = 'Monthly',
    Quarterly = 'Quarterly',
    Annually = 'Annually'
}

export interface Connection {
    id: string;
    name: string;
    role?: string;
    company?: string;
    lastContactDate: string; // ISO string
    contactFrequency: ContactFrequency;
    createdAt: string; // ISO string
}

export interface ConnectionsContextType {
    connections: Connection[];
    addConnection: (connection: Omit<Connection, 'id' | 'createdAt' | 'lastContactDate'>) => Promise<void>;
    updateConnection: (updatedConnection: Connection) => Promise<void>;
    deleteConnection: (connectionId: string) => Promise<void>;
    isLoading: boolean;
}