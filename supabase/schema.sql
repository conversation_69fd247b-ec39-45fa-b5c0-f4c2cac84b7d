-- Run this in Supabase SQL Editor once

-- enable pgcrypto for gen_random_uuid if not already
create extension if not exists pgcrypto;

-- Tasks
create table if not exists public.tasks (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  title text not null,
  description text,
  due_date date not null,
  due_time time not null,
  is_completed boolean not null default false,
  completed_at timestamptz,
  priority text not null,
  category text,
  is_recurring boolean not null default false,
  recurrence_type text,
  recurrence_interval integer,
  created_at timestamptz not null default now()
);
alter table public.tasks enable row level security;
drop policy if exists "tasks_select_own" on public.tasks;
drop policy if exists "tasks_insert_own" on public.tasks;
drop policy if exists "tasks_update_own" on public.tasks;
drop policy if exists "tasks_delete_own" on public.tasks;
create policy "tasks_select_own" on public.tasks for select using (auth.uid() = user_id);
create policy "tasks_insert_own" on public.tasks for insert with check (auth.uid() = user_id);
create policy "tasks_update_own" on public.tasks for update using (auth.uid() = user_id);
create policy "tasks_delete_own" on public.tasks for delete using (auth.uid() = user_id);

-- Settings
create table if not exists public.settings (
  user_id uuid not null references auth.users(id) on delete cascade,
  key text not null,
  value text not null,
  primary key (user_id, key)
);
alter table public.settings enable row level security;
drop policy if exists "settings_select_own" on public.settings;
drop policy if exists "settings_upsert_own" on public.settings;
drop policy if exists "settings_update_own" on public.settings;
drop policy if exists "settings_delete_own" on public.settings;
create policy "settings_select_own" on public.settings for select using (auth.uid() = user_id);
create policy "settings_upsert_own" on public.settings for insert with check (auth.uid() = user_id);
create policy "settings_update_own" on public.settings for update using (auth.uid() = user_id);
create policy "settings_delete_own" on public.settings for delete using (auth.uid() = user_id);

-- Accounts
create table if not exists public.accounts (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  name text not null,
  type text not null,
  balance numeric not null,
  credit_limit numeric,
  apr numeric,
  institution text,
  last_four_digits text,
  payment_day integer,
  default_payment_method text,
  created_at timestamptz not null default now()
);
alter table public.accounts enable row level security;
drop policy if exists "accounts_select_own" on public.accounts;
drop policy if exists "accounts_insert_own" on public.accounts;
drop policy if exists "accounts_update_own" on public.accounts;
drop policy if exists "accounts_delete_own" on public.accounts;
create policy "accounts_select_own" on public.accounts for select using (auth.uid() = user_id);
create policy "accounts_insert_own" on public.accounts for insert with check (auth.uid() = user_id);
create policy "accounts_update_own" on public.accounts for update using (auth.uid() = user_id);
create policy "accounts_delete_own" on public.accounts for delete using (auth.uid() = user_id);

-- Bills
create table if not exists public.bills (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  account_id uuid not null references public.accounts(id) on delete cascade,
  due_date date not null,
  min_payment numeric not null,
  statement_balance numeric not null,
  paid_amount numeric,
  paid_date date,
  is_paid boolean not null default false,
  payment_method text not null,
  created_at timestamptz not null default now(),
  notes text,
  extra_principal numeric
);
alter table public.bills enable row level security;
drop policy if exists "bills_select_own" on public.bills;
drop policy if exists "bills_insert_own" on public.bills;
drop policy if exists "bills_update_own" on public.bills;
drop policy if exists "bills_delete_own" on public.bills;
create policy "bills_select_own" on public.bills for select using (auth.uid() = user_id);
create policy "bills_insert_own" on public.bills for insert with check (auth.uid() = user_id);
create policy "bills_update_own" on public.bills for update using (auth.uid() = user_id);
create policy "bills_delete_own" on public.bills for delete using (auth.uid() = user_id);

-- Connections
create table if not exists public.connections (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  name text not null,
  role text,
  company text,
  last_contact_date date not null,
  contact_frequency text not null,
  created_at timestamptz not null default now()
);
alter table public.connections enable row level security;
drop policy if exists "connections_select_own" on public.connections;
drop policy if exists "connections_insert_own" on public.connections;
drop policy if exists "connections_update_own" on public.connections;
drop policy if exists "connections_delete_own" on public.connections;
create policy "connections_select_own" on public.connections for select using (auth.uid() = user_id);
create policy "connections_insert_own" on public.connections for insert with check (auth.uid() = user_id);
create policy "connections_update_own" on public.connections for update using (auth.uid() = user_id);
create policy "connections_delete_own" on public.connections for delete using (auth.uid() = user_id);

