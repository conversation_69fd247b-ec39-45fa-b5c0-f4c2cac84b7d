-- Run this in Supabase SQL Editor once

-- enable pgcrypto for gen_random_uuid if not already
create extension if not exists pgcrypto;

-- Tasks
create table if not exists public.tasks (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  title text not null,
  description text,
  due_date date not null,
  due_time time not null,
  is_completed boolean not null default false,
  completed_at timestamptz,
  priority text not null,
  category text,
  is_recurring boolean not null default false,
  recurrence_type text,
  recurrence_interval integer,
  created_at timestamptz not null default now()
);
alter table public.tasks enable row level security;
create policy if not exists "tasks_select_own" on public.tasks for select using (auth.uid() = user_id);
create policy if not exists "tasks_insert_own" on public.tasks for insert with check (auth.uid() = user_id);
create policy if not exists "tasks_update_own" on public.tasks for update using (auth.uid() = user_id);
create policy if not exists "tasks_delete_own" on public.tasks for delete using (auth.uid() = user_id);

-- Settings
create table if not exists public.settings (
  user_id uuid not null references auth.users(id) on delete cascade,
  key text not null,
  value text not null,
  primary key (user_id, key)
);
alter table public.settings enable row level security;
create policy if not exists "settings_select_own" on public.settings for select using (auth.uid() = user_id);
create policy if not exists "settings_upsert_own" on public.settings for insert with check (auth.uid() = user_id);
create policy if not exists "settings_update_own" on public.settings for update using (auth.uid() = user_id);
create policy if not exists "settings_delete_own" on public.settings for delete using (auth.uid() = user_id);

-- Accounts
create table if not exists public.accounts (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  name text not null,
  type text not null,
  balance numeric not null,
  credit_limit numeric,
  apr numeric,
  institution text,
  last_four_digits text,
  payment_day integer,
  default_payment_method text,
  created_at timestamptz not null default now()
);
alter table public.accounts enable row level security;
create policy if not exists "accounts_select_own" on public.accounts for select using (auth.uid() = user_id);
create policy if not exists "accounts_insert_own" on public.accounts for insert with check (auth.uid() = user_id);
create policy if not exists "accounts_update_own" on public.accounts for update using (auth.uid() = user_id);
create policy if not exists "accounts_delete_own" on public.accounts for delete using (auth.uid() = user_id);

-- Bills
create table if not exists public.bills (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  account_id uuid not null references public.accounts(id) on delete cascade,
  due_date date not null,
  min_payment numeric not null,
  statement_balance numeric not null,
  paid_amount numeric,
  paid_date date,
  is_paid boolean not null default false,
  payment_method text not null,
  created_at timestamptz not null default now(),
  notes text,
  extra_principal numeric
);
alter table public.bills enable row level security;
create policy if not exists "bills_select_own" on public.bills for select using (auth.uid() = user_id);
create policy if not exists "bills_insert_own" on public.bills for insert with check (auth.uid() = user_id);
create policy if not exists "bills_update_own" on public.bills for update using (auth.uid() = user_id);
create policy if not exists "bills_delete_own" on public.bills for delete using (auth.uid() = user_id);

-- Connections
create table if not exists public.connections (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  name text not null,
  role text,
  company text,
  last_contact_date date not null,
  contact_frequency text not null,
  created_at timestamptz not null default now()
);
alter table public.connections enable row level security;
create policy if not exists "connections_select_own" on public.connections for select using (auth.uid() = user_id);
create policy if not exists "connections_insert_own" on public.connections for insert with check (auth.uid() = user_id);
create policy if not exists "connections_update_own" on public.connections for update using (auth.uid() = user_id);
create policy if not exists "connections_delete_own" on public.connections for delete using (auth.uid() = user_id);

